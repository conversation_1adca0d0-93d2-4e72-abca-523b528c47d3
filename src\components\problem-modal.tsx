import { useState } from "react";
import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const PROBLEM_OPTIONS = [
  "Send To Manager For Review",
  "User Deleted Account",
  "Freeloader",
  "Client hasn't answered 2 messages",
  "Underage",
  "Block IP",
  "Writing to Many Profiles",
  "Claim Fake Profile",
  "Other",
];

interface ProblemModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (selected: string | null) => void;
  isLoading?: boolean;
}

export function ProblemModal({ open, onOpenChange, onSave, isLoading }: ProblemModalProps) {
  const [selected, setSelected] = useState<string | null>(null);

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent width="max-w-xl" className="p-0 rounded-xl bg-card">
        <AlertDialogHeader className="px-6 pt-6 pb-2 text-left">
          <AlertDialogTitle className="text-2xl font-semibold">Problem</AlertDialogTitle>
        </AlertDialogHeader>
        <div className="px-6">
          <div className="grid grid-cols-3 gap-3 mb-2">
            {PROBLEM_OPTIONS.map((opt) => (
              <button
                key={opt}
                type="button"
                className={cn(
                  "border rounded-lg p-3 text-sm text-center transition-colors",
                  selected === opt
                    ? "bg-primary text-primary-foreground border-primary"
                    : "bg-card text-card-foreground border-border hover:bg-accent hover:text-accent-foreground"
                )}
                onClick={() => setSelected(opt)}
              >
                {opt}
              </button>
            ))}
          </div>
        </div>
        <AlertDialogFooter className="px-6 pb-6 pt-2 flex-row-reverse gap-2">
          <AlertDialogCancel className="min-w-[120px] border-gray-300 bg-transparent cursor-pointer">
            Cancel
          </AlertDialogCancel>
          <Button
            onClick={() => onSave(selected)}
            disabled={isLoading || !selected}
            className="min-w-[120px] bg-[#171717] cursor-pointer"
          >
            Save
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
