import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useMemo, useState } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { getCountryList, getMasterApi } from '@/features/members/api'
import { ADD_CITIES, CITY_PLACEHOLDER } from '@/utils/constant'

interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
  readonly onFilterChanged?: any
}

const domainOptions = [
  'Fashion',
  'Fitness',
  'Adult',
  'Beauty',
  'Travel',
  'Other'
]


export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const { data: masterData } = getMasterApi()
  const { data: countryList } = getCountryList()
  const [countries, setCountries] = useState<any>(countryList?.country || [])

  const masterOptions = useMemo(() => {
    const master = masterData?.master || {};
    return {
      model_group: master.model_group || [],
      domain: master.domain || [],
    };
  }, [masterData]);

  // Use model_group from masterOptions as groupOptions
  const groupOptions = masterOptions.model_group || [];

  const buyerStatusOptions = ['Buyer', 'Non Buyer'];
  const userStatusOptions = ['Active', 'Deleted', 'Incomplete'];
  const affiliateStatusOptions = ['Affiliate', 'Non-affiliate'];
  const onlineStatusOptions = ['Online', 'Offline'];
  const pictureStatusOptions = ['Approved', 'Non Approved'];

  const [filters, setFilters] = useState<{
    domainType: string;
    country: string;
    city: string;
    cityInput: string;
    buyerStatus?: string;
    userStatus: string;
    affiliateStatus?: string;
    onlineStatus?: string;
    pictureStatus?: string;
    group: string;
  }>({
    domainType: '',
    country: '',
    city: '',
    cityInput: '',
    buyerStatus: '',
    userStatus: '',
    affiliateStatus: '',
    onlineStatus: '',
    pictureStatus: '',
    group: '',
  })

  const [hasSearched, setHasSearched] = useState(false)
  const [cityDropdownOpen, setCityDropdownOpen] = useState(false)
  const [initialFilters, setInitialFilters] = useState<{
    domainType: string;
    country: string;
    city: string;
    cityInput: string;
    buyerStatus?: string;
    userStatus: string;
    affiliateStatus?: string;
    onlineStatus?: string;
    pictureStatus?: string;
    group: string;
  }>({
    domainType: '',
    country: '',
    city: '',
    cityInput: '',
    buyerStatus: '',
    userStatus: '',
    affiliateStatus: '',
    onlineStatus: '',
    pictureStatus: '',
    group: '',
  })

  const handleFilterChange = (
    key: 'domainType' | 'country' | 'city' | 'cityInput' | 'buyerStatus' | 'userStatus' | 'affiliateStatus' | 'onlineStatus' | 'pictureStatus' | 'group',
    value: string | number[] | string[]
  ) => {
    let newValue = value;
    // For multi-selects, always store as comma-separated string
    if ([
      'domainType', 'country', 'city', 'userStatus', 'group'
    ].includes(key)) {
      if (Array.isArray(value)) {
        newValue = value.filter(Boolean).join(',');
      }
    }
    setFilters((prev) => ({ ...prev, [key]: newValue }));
    console.log({ ...filters, [key]: newValue });
  }

  const handleSearch = () => {
    // For multi-selects, split the string to get the array
    if (filters.domainType) {
      const selected = filters.domainType.split(',').filter(Boolean);
      const domainTypeColumn = table.getColumn('domainType')
      if (domainTypeColumn) {
        domainTypeColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string[]) => {
          return filterValue.includes(row.original.domainType)
        }
        domainTypeColumn.setFilterValue(selected)
      }
    }
    if (filters.country) {
      const selected = filters.country.split(',').map(Number).filter(Boolean);
      const countryColumn = table.getColumn('country')
      if (countryColumn) {
        countryColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: number[]) => {
          return filterValue.includes(row.original.country)
        }
        countryColumn.setFilterValue(selected)
      }
    }
    if (filters.city) {
      const selected = filters.city.split(',').filter(Boolean);
      const cityColumn = table.getColumn('city')
      if (cityColumn) {
        cityColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string[]) => {
          return filterValue.includes(row.original.city)
        }
        cityColumn.setFilterValue(selected)
      }
    }
    if (filters.buyerStatus) {
      const buyerStatusColumn = table.getColumn('buyerStatus')
      if (buyerStatusColumn) {
        buyerStatusColumn.setFilterValue(filters.buyerStatus)
      }
    }
    if (filters.userStatus) {
      const selected = filters.userStatus.split(',').filter(Boolean);
      const userStatusColumn = table.getColumn('userStatus')
      if (userStatusColumn) {
        userStatusColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string[]) => {
          return filterValue.includes(row.original.userStatus)
        }
        userStatusColumn.setFilterValue(selected)
      }
    }
    if (filters.affiliateStatus) {
      const affiliateStatusColumn = table.getColumn('affiliateStatus')
      if (affiliateStatusColumn) {
        affiliateStatusColumn.setFilterValue(filters.affiliateStatus)
      }
    }
    if (filters.onlineStatus) {
      const onlineStatusColumn = table.getColumn('onlineStatus')
      if (onlineStatusColumn) {
        onlineStatusColumn.setFilterValue(filters.onlineStatus)
      }
    }
    if (filters.pictureStatus) {
      const pictureStatusColumn = table.getColumn('pictureStatus')
      if (pictureStatusColumn) {
        pictureStatusColumn.setFilterValue(filters.pictureStatus)
      }
    }
    if (filters.group) {
      const selected = filters.group.split(',').map(Number).filter(Boolean);
      const groupColumn = table.getColumn('group')
      if (groupColumn) {
        groupColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: number[]) => {
          return filterValue.includes(row.original.group)
        }
        groupColumn.setFilterValue(selected)
      }
    }
    setHasSearched(true)
    setInitialFilters({ ...filters })
    onFilterChanged(filters, 1)
  }

  const handleReset = () => {
    table.resetColumnFilters()
    const f: any = {
      domainType: '',
      country: '',
      city: '',
      cityInput: '',
      buyerStatus: '',
      userStatus: '',
      affiliateStatus: '',
      onlineStatus: '',
      pictureStatus: '',
      group: '',
    }
    setFilters(f)
    setInitialFilters(f)
    setHasSearched(false)
    onFilterChanged(f, 0)
  }

  const hasFilterChanges = Boolean(
    filters.domainType !== initialFilters.domainType ||
    filters.country !== initialFilters.country ||
    filters.city !== initialFilters.city ||
    filters.buyerStatus !== initialFilters.buyerStatus ||
    filters.userStatus !== initialFilters.userStatus ||
    filters.affiliateStatus !== initialFilters.affiliateStatus ||
    filters.onlineStatus !== initialFilters.onlineStatus ||
    filters.pictureStatus !== initialFilters.pictureStatus ||
    filters.group !== initialFilters.group
  )

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center gap-4 flex-wrap'>
        {/* Multi-select popover for domainType */}
        <Popover>
          <PopoverTrigger asChild>
            <div className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.domainType && "text-muted-foreground"
            )}>
              {filters.domainType === '' && <span>Select domain type</span>}
              {filters.domainType && filters.domainType.split(',').filter(Boolean).slice(0, 2).map((val: string) => (
                <div className="flex gap-1 items-center bg-muted rounded p-1" key={val}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                    {val}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer" onClick={e => {
                    e.stopPropagation();
                    handleFilterChange('domainType', filters.domainType.split(',').filter(Boolean).filter((v: string) => v !== val))
                  }} />
                </div>
              ))}
              {filters.domainType && filters.domainType.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search domain type..." />
              <CommandEmpty>No domain type found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const sortedDomainTypes = [
                    ...domainOptions.filter((type) => filters.domainType.split(',').includes(type)),
                    ...domainOptions.filter((type) => !filters.domainType.split(',').includes(type)),
                  ];
                  return sortedDomainTypes.map((type) => (
                    <CommandItem
                      key={type}
                      onSelect={() => {
                        const currentValues = filters.domainType.split(',').filter(Boolean);
                        const selected = currentValues.includes(type)
                          ? currentValues.filter((v: string) => v !== type)
                          : [...currentValues, type];
                        handleFilterChange('domainType', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{type}</span>
                      {filters.domainType.split(',').includes(type) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-select popover for country */}
        <Popover>
          <PopoverTrigger asChild>
            <div className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.country && "text-muted-foreground"
            )}>
              {filters.country === '' && <span>Select country</span>}
              {filters.country && filters.country.split(',').slice(0, 2).map((id: string) => {
                const country = countries.find((c: any) => c.id === Number(id));
                return (
                  <div className="flex gap-1 items-center bg-muted rounded p-1" key={id}>
                    <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                      {country?.name ?? id}
                    </Badge>
                    <X className="h-3 w-3 cursor-pointer" onClick={e => {
                      e.stopPropagation();
                      handleFilterChange('country', filters.country.split(',').filter(Boolean).filter((v: string) => v !== id))
                    }} />
                  </div>
                )
              })}
              {filters.country && filters.country.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search country..." />
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const sortedCountries = [
                    ...countries.filter((c: any) => filters.country.split(',').includes(String(c.id))),
                    ...countries.filter((c: any) => !filters.country.split(',').includes(String(c.id))),
                  ];
                  return sortedCountries.map((country: any) => (
                    <CommandItem
                      key={country.id}
                      onSelect={() => {
                        const currentValues = filters.country.split(',').filter(Boolean);
                        const selected = currentValues.includes(String(country.id))
                          ? currentValues.filter((v: string) => v !== String(country.id))
                          : [...currentValues, String(country.id)];
                        handleFilterChange('country', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{country.name}</span>
                      {filters.country.split(',').includes(String(country.id)) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-input for cities with dropdown for selected */}
        <div className="relative w-[200px]">
          <input
            type="text"
            className="w-full border border-input bg-card text-foreground rounded-md px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground"
            placeholder={filters.city.split(',').filter(Boolean).length === 0 ? CITY_PLACEHOLDER : ADD_CITIES}
            value={filters.cityInput || ""}
            onFocus={() => setCityDropdownOpen(true)}
            onBlur={() => setTimeout(() => setCityDropdownOpen(false), 150)}
            onChange={e => handleFilterChange('cityInput', e.target.value)}
            onKeyDown={e => {
              if (
                (e.key === 'Enter' || e.key === ',') &&
                filters.cityInput &&
                !filters.city.split(',').filter(Boolean).includes(filters.cityInput.trim())
              ) {
                handleFilterChange('city', [...filters.city.split(',').filter(Boolean), filters.cityInput.trim()]);
                handleFilterChange('cityInput', '');
                e.preventDefault();
              }
              if (e.key === 'Backspace' && !filters.cityInput && filters.city) {
                // Remove last city on backspace if input is empty
                handleFilterChange('city', filters.city.split(',').filter(Boolean).slice(0, -1));
              }
            }}
          />
          {cityDropdownOpen && filters.city && (
            <div className="absolute left-0 right-0 mt-1 bg-popover border border-input rounded-md shadow-lg z-10 p-2 flex flex-wrap gap-1">
              {filters.city.split(',').filter(Boolean).map((city: string) => (
                <div className="flex gap-1 items-center bg-muted rounded p-1" key={city}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                    {city}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer"
                    onMouseDown={e => e.preventDefault()}
                    onClick={() => {
                      handleFilterChange('city', filters.city.split(',').filter(Boolean).filter((v: string) => v !== city))
                    }} />
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Buyer Status single-select dropdown */}
        {/* <FilterSelect
          value={filters.buyerStatus || ''}
          placeholder="Select Buyer Status"
          options={buyerStatusOptions}
          onChange={(value) => handleFilterChange('buyerStatus', value || '')}
        /> */}
        {/* User Status multi-select popover */}
        <Popover>
          <PopoverTrigger asChild>
            <div className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.userStatus && "text-muted-foreground"
            )}>
              {filters.userStatus === '' && <span>Select user status</span>}
              {filters.userStatus && filters.userStatus.split(',').filter(Boolean).slice(0, 2).map((val: string) => (
                <div className="flex gap-1 items-center bg-muted rounded p-1" key={val}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                    {val}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer" onClick={e => {
                    e.stopPropagation();
                    handleFilterChange('userStatus', filters.userStatus.split(',').filter(Boolean).filter((v: string) => v !== val))
                  }} />
                </div>
              ))}
              {filters.userStatus && filters.userStatus.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search user status..." />
              <CommandEmpty>No user status found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const sortedUserStatus = [
                    ...userStatusOptions.filter((status) => filters.userStatus.split(',').includes(status)),
                    ...userStatusOptions.filter((status) => !filters.userStatus.split(',').includes(status)),
                  ];
                  return sortedUserStatus.map((status) => (
                    <CommandItem
                      key={status}
                      onSelect={() => {
                        const currentValues = filters.userStatus.split(',').filter(Boolean);
                        const selected = currentValues.includes(status)
                          ? currentValues.filter((v: string) => v !== status)
                          : [...currentValues, status];
                        handleFilterChange('userStatus', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{status}</span>
                      {filters.userStatus.split(',').includes(status) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Affiliate Status single-select dropdown */}
        {/* <FilterSelect
          value={filters.affiliateStatus || ''}
          placeholder="Select Affiliate Status"
          options={affiliateStatusOptions}
          onChange={(value) => handleFilterChange('affiliateStatus', value || '')}
        /> */}
        {/* Online Status single-select dropdown */}
        {/* <FilterSelect
          value={filters.onlineStatus || ''}
          placeholder="Select Online Status"
          options={onlineStatusOptions}
          onChange={(value) => handleFilterChange('onlineStatus', value || '')}
        /> */}
        {/* Picture Status single-select dropdown */}
        {/* <FilterSelect
          value={filters.pictureStatus || ''}
          placeholder="Select Picture Status"
          options={pictureStatusOptions}
          onChange={(value) => handleFilterChange('pictureStatus', value || '')}
        /> */}
        {/* Group multi-select popover */}
        <Popover>
          <PopoverTrigger asChild>
            <div className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.group && "text-muted-foreground"
            )}>
              {filters.group === '' && <span>Select group</span>}
              {filters.group && filters.group.split(',').filter(Boolean).slice(0, 2).map((id: string) => {
                const group = groupOptions.find((g: any) => g.id === Number(id));
                return (
                  <div className="flex gap-1 items-center bg-muted rounded p-1" key={id}>
                    <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                      {group?.title ?? id}
                    </Badge>
                    <X className="h-3 w-3 cursor-pointer" onClick={e => {
                      e.stopPropagation();
                      handleFilterChange('group', filters.group.split(',').filter(Boolean).filter((v: string) => v !== id))
                    }} />
                  </div>
                )
              })}
              {filters.group && filters.group.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search group..." />
              <CommandEmpty>No group found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const sortedGroups = [
                    ...groupOptions.filter((group: any) => filters.group.split(',').includes(String(group.id))),
                    ...groupOptions.filter((group: any) => !filters.group.split(',').includes(String(group.id))),
                  ];
                  return sortedGroups.map((group: any) => (
                    <CommandItem
                      key={group.id}
                      onSelect={() => {
                        const currentValues = filters.group.split(',').filter(Boolean);
                        const selected = currentValues.includes(String(group.id))
                          ? currentValues.filter((v: string) => v !== String(group.id))
                          : [...currentValues, String(group.id)];
                        handleFilterChange('group', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{group.title}</span>
                      {filters.group.split(',').includes(String(group.id)) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* DatePicker components removed */}
        <Button
          onClick={handleSearch}
          className="h-8 px-3"
          disabled={!hasFilterChanges}
        >
          Search
        </Button>
        {(isFiltered || hasSearched) && (
          <Button
            variant='outline'
            onClick={handleReset}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
          </Button>
        )}
      </div>
    </div>
  )
}
