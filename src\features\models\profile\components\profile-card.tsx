import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { IconDotsVertical } from '@tabler/icons-react'
import { Model } from '../../data/models'

interface ProfileCardProps {
  model: Model
}

export default function ProfileCard({ model }: ProfileCardProps) {
  // Get initials from profile name
  const initials = model.profile.split(' ').map(n => n[0]).join('').toUpperCase()

  // Profile details
  const details = [
    { label: 'Role', value: 'Model User' },
    { label: 'Mobile', value: '(44) 123 456 123' },
    { label: 'Email', value: model.email },
    { label: 'Joined', value: '23 Jan 2023' },
    { label: 'Minimum Payout', value: '0.00' },
    { label: 'Payment Currency', value: 'EUR' },
    { label: 'Payment Method', value: 'Yoursafe' },
    { label: 'Beneficiary Account Name', value: 'print_mod' },
    { label: 'Beneficiary Account Address', value: 'Address will go here' },
    { label: 'Beneficiary Country', value: 'India' },
    { label: 'Beneficiary City', value: '26 Panjagutta' },
    { label: 'Beneficiary Bank', value: 'Test' },
    { label: 'Beneficiary Bank Address', value: 'Test' },
    { label: 'Account / Wallet Number', value: '4GB950478652' },
    { label: 'Bank Code', value: '7852' },
    { label: 'Personal ID / Photo', value: 'photo.png' },
  ]

  // Stats data
  const stats = [
    { label: 'Reply Percentage', value: '50%' },
    { label: 'Average Character / Message', value: '150' },
    { label: 'Total Hole Messages', value: '123' },
  ]

  return (
    <div className="space-y-6">
      {/* Profile Card */}
      <div className="bg-white rounded-lg border overflow-hidden">
        {/* Profile Header */}
        <div className="p-6 pb-4">
          <div className="flex items-start justify-between mb-4">
            <div className="flex flex-col items-center w-full">
              {/* Avatar with status */}
              <div className="relative mb-3">
                <Avatar className="h-20 w-20">
                  <AvatarImage src="" alt={model.profile} />
                  <AvatarFallback className="text-xl font-semibold bg-gray-200">
                    {initials}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-1 -right-1 h-5 w-5 bg-green-500 border-2 border-white rounded-full"></div>
              </div>

              {/* Name */}
              <h2 className="text-lg font-bold text-center">{model.profile}</h2>
            </div>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="absolute top-4 right-4">
                  <IconDotsVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Edit Profile Details</DropdownMenuItem>
                <DropdownMenuItem>Reset Password</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">Block User</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Profile Details */}
        <div className="px-6 pb-6">
          <div className="space-y-3">
            {details.map((detail) => (
              <div key={detail.label} className="flex justify-between items-center py-1">
                <div className="text-xs text-gray-500 font-medium">{detail.label}</div>
                <div className="text-xs text-gray-900 text-right max-w-[60%] truncate">{detail.value}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Stats Card */}
      <div className="bg-white rounded-lg border p-6">
        <div className="space-y-4">
          {stats.map((stat) => (
            <div key={stat.label} className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-lg font-bold text-gray-600">$</span>
                <span className="text-sm font-medium text-gray-700">{stat.label}</span>
              </div>
              <p className="text-xl font-bold text-gray-900">{stat.value}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
