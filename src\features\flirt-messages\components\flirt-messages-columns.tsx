import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import LongText from '@/components/long-text'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<any>[] = [
    {
        accessorKey: 'serialNumber',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='#' />
        ),
        cell: ({ row }) => (
            <div className='w-8'>{row.getValue('serialNumber')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'w-8'
            ),
        },
        enableHiding: false,
    },
    {
        accessorKey: 'message',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Message' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.getValue('message')}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        id: 'actions',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Action' />
        ),
        enableSorting: false,
        cell: DataTableRowActions,
        meta: { className: 'w-16' },
    },
]
