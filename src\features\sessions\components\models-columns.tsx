import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import LongText from '@/components/long-text'
import { Model } from '../data/models'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<Model>[] = [
  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='#' />
    ),
    cell: ({ row }) => (
      <div className='w-8'>{row.getValue('serialNumber')}</div>
    ),
    meta: {
      className: cn(
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'w-8'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'profile',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Profile' />
    ),
    cell: ({ row }) => {
      const profile = row?.getValue('profile') as string

      return (
        <div className='flex items-center gap-3'>
          <Avatar className='h-8 w-8'>
            <AvatarImage src="" alt={profile} />
            <AvatarFallback className='text-xs bg-gray-200'>
              {row?.original?.username[0].toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <LongText className='max-w-36'>{profile}</LongText>
        </div>
      )
    },
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-0 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'username',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Username' />
    ),
    cell: ({ row }) => {

      return (
        <LongText className='max-w-36'>{row.getValue('username')}</LongText>
      )
    },
    meta: { className: 'w-36' },
  },
  {
    accessorKey: 'model_profile.country',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Country / City' />
    ),
    cell: ({ row }: any) => (
      <LongText className='max-w-36'> {`${row?.original?.model_profile?.country}/${row?.original?.model_profile?.city}` || "N/A"}</LongText>
    ),
    meta: { className: 'w-36' },
  },
  {
    accessorKey: 'profilePic',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Profile Pic / Extra Pic' />
    ),
    cell: ({ row }) => (
      <div className='text-center'>7/28</div>
    ),
    meta: { className: 'w-32' },
  },
  {
    accessorKey: 'customerMsg',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Customer Msg / Model Msg' />
    ),
    cell: ({ row }) => (
      <div className='text-center'>125/35</div>
    ),
    meta: { className: 'w-32' },
  },
  {
    accessorKey: 'model_profile.domainType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Domain Type' />
    ),
    cell: ({ row }: any) => (
      <Badge variant="secondary">{row?.original?.model_profile?.domainType || "N/A"}</Badge>
    ),
    meta: { className: 'w-24' },
  },
  {
    accessorKey: 'model_profile.modelGroup',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Group' />
    ),
    cell: ({ row }: any) => (
      <Badge variant="outline">{row?.original?.model_profile?.modelGroup || "N/A"}</Badge>
    ),
    meta: { className: 'w-20' },
  },
  {
    accessorKey: 'botOn',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Bot On?' />
    ),
    cell: ({ row }) => (
      <div className='text-center'>
        {row.getValue('botOn') ? 'Yes' : 'No'}
      </div>
    ),
    meta: { className: 'w-20' },
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Action' />
    ),
    enableSorting: false,
    cell: DataTableRowActions,
    meta: { className: 'w-16' },
  },
]
