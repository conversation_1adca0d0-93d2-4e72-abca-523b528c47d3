import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import {
    IconEdit, IconUser, IconUsersGroup, IconClock,
    IconLanguage,
    IconPaperclip,
    IconGift,
    IconMoodSmile,
    IconMessageReply,
    IconDashboard,
    IconShareplay,
    IconSearch,
    IconList,
    IconArrowDown,
    IconArrowUp
} from "@tabler/icons-react";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@radix-ui/react-scroll-area";
import UserImg from '@/assets/user.png'
import { ConfirmDialog } from "@/components/modal";
import { useState } from "react";
import { HoldMessageModal } from "@/components/hold-message-modal";
import { ProblemModal } from "@/components/problem-modal";

export default function ChatProfileView() {
    const [show, setShow] = useState(false);
    const [showHold, setShowHold] = useState(false);
    const [showProblem, setShowProblem] = useState(false);
    return (
        <div className="grid grid-cols-1 xl:grid-cols-[1fr_2fr_1fr] h-screen w-full gap-[16px] px-3 py-4 sm:px-4 sm:py-6">
            {/* Left User Panel */}
            <div className="flex flex-col gap-[16px]">
                <div className="bg-sidebar p-4 rounded-2xl">
                    <Avatar className="w-[54px] h-[54px] mb-[12px] rounded-full bg-[#999] text-white flex items-center justify-center">
                        <AvatarImage src="/avatar1.png" alt="Fit-n_Sexy_milf" />
                        <AvatarFallback className="text-lg">FS</AvatarFallback>
                    </Avatar>
                    <div className="text-lg font-medium">Fit-n_Sexy_milf (49)</div>
                    <div className="text-sm">Portarlington - VTL: 0</div>
                    <div className="text-sm">Timezone: GMT+5:30</div>
                    <hr className="my-3"></hr>
                    <div className="flex flex-wrap">
                        <div className="basis-1/2">
                            <div className="text-sm">Relation Status</div>
                            <div className="text-sm font-semibold">Married</div>
                        </div>
                        <div className="basis-1/2">
                            <div className="text-sm">Location</div>
                            <div className="text-sm font-semibold">United Kingdom</div>
                        </div>
                        <div className="basis-2/2 mt-3">
                            <div className="text-sm">Interests</div>
                            <div className="text-sm font-semibold">Sex toys, Lingerie, Soft sex, Oral/69, Long foreplay, Exhibitionism, Hardcore, Wild and crazy, Try everything</div>
                        </div>
                    </div>
                </div>

                <div>
                    <div className="text-sm font-semibold mb-[10px]">Affiliate</div>
                    <div className="bg-sidebar p-4 rounded-2xl">
                        <select className="block w-full bg-sidebar text-sm outline-none">
                            <option> Yes </option>
                            <option> No </option>
                        </select>
                    </div>
                </div>

                <div className="bg-sidebar p-4 rounded-2xl">
                    <div className="text-base font-semibold mb-[12px]">Notes</div>
                    <div className="flex mb-4 overflow-x-auto space-x-3 flex-nowrap">
                        <div className="bg-sidebar-accent p-3 rounded-lg min-w-[200px]">
                            <div className="text-sm">He is back after 3 days.</div>
                            <hr className="my-3"></hr>
                            <div className="flex gap-4 items-center justify-between">
                                <div className="text-xs">Edited : Dec 2, 2024</div>
                                <div></div>
                            </div>
                        </div>
                    </div>
                    <Input type="text" placeholder="Enter Note" className="text-sm h-[42px] shadow-none" />
                </div>

                <div className="flex flex-col gap-3 bg-sidebar p-4 rounded-2xl">
                    <div className="bg-sidebar-accent p-3 rounded-lg">
                        <div className="flex gap-4 items-center justify-between">
                            <div className="flex items-center gap-1 text-sm"><IconUser width='18px' /> Name</div>
                            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background"><IconEdit width='14px' /></button>
                        </div>
                        <hr className="my-3"></hr>
                        <ul className="list-disc ps-5">
                            <li className="text-xs">John Doe</li>
                        </ul>
                    </div>
                    <div className="bg-sidebar-accent p-3 rounded-lg">
                        <div className="flex gap-4 items-center justify-between">
                            <div className="flex items-center gap-1 text-sm"><IconUser width='18px' /> Living Conditions</div>
                            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background"><IconEdit width='14px' /></button>
                        </div>
                        <hr className="my-3"></hr>
                        <ul className="list-disc ps-5">
                            <li className="text-xs">Lives in Fulham</li>
                            <li className="text-xs">Splits bills with partner</li>
                        </ul>
                    </div>
                    <div className="bg-sidebar-accent p-3 rounded-lg">
                        <div className="flex gap-4 items-center justify-between">
                            <div className="flex items-center gap-1 text-sm"><IconUsersGroup width='18px' /> Family / Pets</div>
                            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background"><IconEdit width='14px' /></button>
                        </div>
                        <hr className="my-3"></hr>
                        <ul className="list-disc ps-5">
                            <li className="text-xs">In a complicated long-term relationship</li>
                            <li className="text-xs">2 Kids boy is 6 and girl is 3 1/2 years old</li>
                        </ul>
                    </div>
                </div>
            </div>


            {/* Center Chat Panel */}
            <div className="flex flex-col rounded-2xl">
                <div className="flex items-center gap-3 p-4 bg-[#171717] border-b rounded-t-2xl">
                    {/* Search Input */}
                    <div className="flex items-center bg-[#444] px-3 py-2 rounded-md text-white w-full max-w-md">
                        <IconSearch size={16} className="mr-2 text-gray-300" />
                        <input
                            type="text"
                            placeholder="Search Keyword"
                            className="bg-transparent text-sm text-white placeholder-gray-300 outline-none w-full"
                        />
                    </div>

                    {/* Stat Box */}
                    <div className="flex items-center bg-[#252525] text-white text-sm px-4 py-2 rounded-xs gap-3">
                        <span className="flex items-center gap-1">
                            00 <IconList size={14} />
                        </span>
                        <div className="border-l border-gray-400 h-4" />
                        <span className="flex items-center gap-1">
                            08 <IconArrowDown size={14} />
                        </span>
                        <div className="border-l border-gray-400 h-4" />
                        <span className="flex items-center gap-1">
                            09 <IconArrowUp size={14} />
                        </span>
                    </div>

                    {/* Hold Button */}
                    <button onClick={() => setShowHold(true)} className="bg-white text-gray-700 text-sm px-4 py-2 rounded-xs cursor-pointer">
                        Hold
                    </button>
                </div>

                <div className="flex-1 bg-sidebar p-4 space-y-4 overflow-y-auto max-h-[calc(100dvh-350px)]">
                    {/* Example message block */}
                    <div className="Received-msg flex gap-2 w-full max-w-[85%]">
                        <img src={UserImg} alt='user' className="w-[40px] h-[40px] rounded-full" />
                        <div className="flex flex-col gap-2">
                            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">next time you'll be awake at this hour why not now</div>
                            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">Didn't I tell you not to put your phone on charge just because it's the weekend?</div>
                            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">🥰🥰😘😘❤️❤️</div>
                            <div className="text-xs text-gray-500">Sat 5:10 AM</div>
                        </div>
                    </div>
                    <div className="sent-msg flex justify-end gap-2 w-full ms-auto max-w-[85%]">
                        <div className="flex flex-col items-end gap-2">
                            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-se-none bg-sidebar-primary text-sm text-white">next time you'll be awake at this hour why not now next time you'll be awake at this hour why not nownext time you'll be awake at this hour why not now</div>
                            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-se-none bg-sidebar-primary text-sm text-white">🥰🥰😘😘❤️❤️</div>
                            <div className="text-xs text-gray-500 text-end">Sat 5:15 AM</div>
                        </div>
                        <img src={UserImg} alt='user' className="w-[40px] h-[40px] rounded-full" />
                    </div>

                    <div className="text-xs text-gray-600 bg-sidebar text-center mt-4 w-fit px-4 py-2 rounded-full mx-auto">Tuesday Dec 2, 2024</div>

                    <div className="Received-msg flex gap-2 w-full max-w-[85%]">
                        <img src={UserImg} alt='user' className="w-[40px] h-[40px] rounded-full" />
                        <div className="flex flex-col gap-2">
                            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">next time you'll be awake at this hour why not now</div>
                            <div className="text-xs text-gray-500">Sat 5:10 AM</div>
                        </div>
                    </div>
                </div>

                <div className="w-full p-4 space-y-4 bg-sidebar rounded-b-2xl">
                    {/* Timer Buttons */}
                    <div className="flex gap-2 mb-2">
                        <button className="flex items-center gap-1 px-4 py-2 text-sm bg-sidebar-accent rounded-md">
                            <IconClock size={16} />
                            266 Sec
                        </button>
                        <button className="px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md">
                            + 60 Sec
                        </button>
                    </div>

                    {/* Message Input */}
                    <div className="flex items-center justify-between border rounded-xl px-4 py-3 bg-sidebar mb-2">
                        <input
                            type="text"
                            placeholder="Enter Message"
                            className="flex-1 outline-none text-md bg-transparent placeholder-gray-400"
                        />
                        <div className="flex items-center gap-3 text-gray-500">
                            <IconLanguage size={24} />
                            <IconPaperclip size={24} />
                            <IconGift size={24} />
                            <IconMoodSmile size={24} />
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-wrap items-center justify-between gap-2">
                        <div className="flex gap-2">
                            <button className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md">
                                <IconShareplay size={16} />
                                Reply & Stay
                            </button>
                            <button className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md">
                                <IconMessageReply size={16} />
                                Reply
                            </button>
                            <button className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md">
                                <IconDashboard size={16} />
                                Lobby
                            </button>
                        </div>
                        <button onClick={() => setShowProblem(true)} className="flex items-center gap-1 px-4 py-2 text-sm border rounded-md cursor-pointer">
                            <IconUsersGroup size={16} />
                            Problem
                        </button>
                    </div>
                </div>
            </div>

            {/* Right User Panel */}
            <div className="flex flex-col gap-[16px]">
                <div className="bg-sidebar p-4 rounded-2xl cursor-pointer" onClick={() => setShow(true)}>
                    <Avatar className="w-[54px] h-[54px] mb-[12px] rounded-full bg-[#999] text-white flex items-center justify-center">
                        <AvatarImage src="/avatar2.png" alt="Daniel Hamilton" />
                        <AvatarFallback>DH</AvatarFallback>
                    </Avatar>
                    <div className="text-lg font-medium">Daniel Hamilton</div>
                    <div className="text-sm">Last Active: 11 Sep 2024, 1:45 PM</div>
                    <div className="text-sm">Timezone: GMT+5:30</div>
                    <hr className="my-3"></hr>
                    <div className="flex flex-wrap">
                        <div className="basis-1/2">
                            <div className="text-sm">Relation Status</div>
                            <div className="text-sm font-semibold">Married</div>
                        </div>
                        <div className="basis-1/2">
                            <div className="text-sm">Location</div>
                            <div className="text-sm font-semibold">United Kingdom</div>
                        </div>
                        <div className="basis-2/2 mt-3">
                            <div className="text-sm">Interests</div>
                            <div className="text-sm font-semibold">Sex toys, Lingerie, Soft sex, Oral/69, Long foreplay, Exhibitionism, Hardcore, Wild and crazy, Try everything</div>
                        </div>
                    </div>
                </div>

                <div>
                    <div className="text-sm font-semibold mb-[10px]">Affiliate</div>
                    <div className="bg-sidebar p-4 rounded-2xl">
                        <select className="block w-full bg-sidebar text-sm outline-none">
                            <option> Yes </option>
                            <option> No </option>
                        </select>
                    </div>
                </div>

                <div className="bg-sidebar p-4 rounded-2xl">
                    <div className="text-base font-semibold mb-[12px]">Notes</div>
                    <div className="flex mb-4 overflow-x-auto space-x-3 flex-nowrap">
                        <div className="bg-sidebar-accent p-3 rounded-lg min-w-[200px]">
                            <div className="text-sm">He is back after 3 days.</div>
                            <hr className="my-3"></hr>
                            <div className="flex gap-4 items-center justify-between">
                                <div className="text-xs">Edited : Dec 2, 2024</div>
                                <div></div>
                            </div>
                        </div>
                    </div>
                    <Input type="text" placeholder="Enter Note" className="text-sm h-[42px] shadow-none" />
                </div>

                <div className="flex flex-col gap-3 bg-sidebar p-4 rounded-2xl">
                    <div className="bg-sidebar-accent p-3 rounded-lg">
                        <div className="flex gap-4 items-center justify-between">
                            <div className="flex items-center gap-1 text-sm"><IconUser width='18px' /> Name</div>
                            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background"><IconEdit width='14px' /></button>
                        </div>
                        <hr className="my-3"></hr>
                        <ul className="list-disc ps-5">
                            <li className="text-xs">John Doe</li>
                        </ul>
                    </div>
                    <div className="bg-sidebar-accent p-3 rounded-lg">
                        <div className="flex gap-4 items-center justify-between">
                            <div className="flex items-center gap-1 text-sm"><IconUser width='18px' /> Living Conditions</div>
                            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background"><IconEdit width='14px' /></button>
                        </div>
                        <hr className="my-3"></hr>
                        <ul className="list-disc ps-5">
                            <li className="text-xs">Lives in Fulham</li>
                            <li className="text-xs">Splits bills with partner</li>
                        </ul>
                    </div>
                    <div className="bg-sidebar-accent p-3 rounded-lg">
                        <div className="flex gap-4 items-center justify-between">
                            <div className="flex items-center gap-1 text-sm"><IconUsersGroup width='18px' /> Family / Pets</div>
                            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background"><IconEdit width='14px' /></button>
                        </div>
                        <hr className="my-3"></hr>
                        <ul className="list-disc ps-5">
                            <li className="text-xs">In a complicated long-term relationship</li>
                            <li className="text-xs">2 Kids boy is 6 and girl is 3 1/2 years old</li>
                        </ul>
                    </div>
                </div>
            </div>

            <ProblemModal
                open={showProblem}
                onOpenChange={setShowProblem}
                title="Are you sure?"
                desc="This action cannot be undone. This will permanently delete the record."
                confirmText="Delete"
                cancelBtnText="Cancel"
                destructive
                isLoading={false}
                handleConfirm={() => { }}
            />

            <HoldMessageModal
                open={showHold}
                onOpenChange={setShowHold}
                title="Are you sure?"
                desc="This action cannot be undone. This will permanently delete the record."
                confirmText="Delete"
                cancelBtnText="Cancel"
                destructive
                isLoading={false}
                handleConfirm={() => { }}
            />

            {/*  */}
            <ConfirmDialog
                open={show}
                onOpenChange={setShow}
                title="Are you sure?"
                desc="This action cannot be undone. This will permanently delete the record."
                confirmText="Delete"
                cancelBtnText="Cancel"
                destructive
                isLoading={false}
                handleConfirm={() => { }}
            />
        </div>
    );
}
