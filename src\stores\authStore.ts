import Cookies from 'js-cookie'
import { create } from 'zustand'

const ACCESS_TOKEN = 'ACCESS_TOKEN'

interface AuthUser {
  accountNo: string
  email: string
  role: string[]
  exp: number
}

interface AuthState {
  auth: {
    user: any
    // user: AuthUser | null
    setUser: (user: any) => void
    // setUser: (user: AuthUser | null) => void
    accessToken: string
    setAccessToken: (accessToken: string) => void
    resetAccessToken: () => void
    reset: () => void,
    showSpinner: boolean,
    setShowSpinner: (show: boolean) => void; // ✅ Add this line

  }
}

export const useAuthStore = create<AuthState>((set) => {
  const cookieState = Cookies.get(ACCESS_TOKEN)
  const initToken = cookieState ?? ''

  return {
    auth: {
      user: null,
      showSpinner: false,
      setUser: (user) => {

        set((state) => ({ ...state, auth: { ...state.auth, user: user } }))
      },
      accessToken: initToken,
      setAccessToken: (accessToken) =>
        set((state) => {
          const token = JSON.stringify(accessToken)
          Cookies.set(ACCESS_TOKEN, token?.replace(/^"|"$/g, '')
          )
          return { ...state, auth: { ...state.auth, accessToken } }
        }),
      setShowSpinner: (show: any) =>
        set((state) => {
          return { ...state, auth: { ...state.auth, showSpinner: show } }
        }),

      resetAccessToken: () =>
        set((state) => {
          Cookies.remove(ACCESS_TOKEN)
          return { ...state, auth: { ...state.auth, accessToken: '' } }
        }),
      reset: () =>
        set((state) => {
          Cookies.remove(ACCESS_TOKEN)
          // Clear remembered credentials
          localStorage.removeItem('rememberedEmail')
          localStorage.removeItem('rememberMe')
          return {
            ...state,
            auth: { ...state.auth, user: null, accessToken: '' },
          }
        }),
    },
  }
})

export const getAccessToken = () => {
  const token = Cookies.get(ACCESS_TOKEN)
  return token ?? ''
} 
