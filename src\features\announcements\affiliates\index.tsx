import { CONSTANTS } from "@/features/members/utils/constant";
import { getActiveAnnouncementsApi, getAllAnnouncementsApi, setPastAnnouncement } from "../api";
import { AnnouncementCard } from "../components/active-announcements";
import PostGrid from "../components/past-grid";

export default function AffiliatesComponent() {
    const { data: { announcements = [] } = {}, refetch } = getActiveAnnouncementsApi({ type: CONSTANTS.AFFILIATE });
    const { data: { announcement: pastAnnouncements = [] } = {}, refetch: refetchAll } = getAllAnnouncementsApi({ type: CONSTANTS.AFFILIATE });
    const { mutateAsync: setPastAnnouncementMutation } = setPastAnnouncement();

    const sentAnnouncementPast = async (_isChecked: boolean, id: any) => {
        try {
            const response: any = await setPastAnnouncementMutation(id);
            refetch();
            refetchAll();
        } catch (error: any) {
            console.log(error);
        }
    };
    return (
        <div className="p-6 space-y-6">
            <div className="flex gap-4">
                {announcements.map((item: any) => (
                    <AnnouncementCard
                        key={item.id}
                        name={item.name}
                        email={item.email}
                        content={item.content}
                        date={item.updatedAt}
                        id={item.id}
                        defaultIsPast={item.isPast}
                        onToggleChange={sentAnnouncementPast}
                    />
                ))}
            </div>

            <div className="space-y-2">
                {pastAnnouncements.length > 0 &&
                    <>
                        <label className="font-medium">Past Affiliate Announcements</label>
                        <PostGrid data={pastAnnouncements} sentAnnouncementPast={sentAnnouncementPast} />
                    </>
                }
            </div>
        </div>
    );
} 