'use client'

import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from '@/components/ui/dialog'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Coins } from 'lucide-react'

const creditFormSchema = z.object({
    username: z.string().min(1, 'Username is required'),
    amount: z
        .string()
        .min(1, 'Amount is required')
        .regex(/^\d+$/, 'Amount must be a number'),
})

export type CreditFormValues = z.infer<typeof creditFormSchema>

interface Props {
    open: boolean
    onOpenChange: (open: boolean) => void
    defaultUsername?: string
    currentBalance?: number
    onSubmit: (data: CreditFormValues) => void
    title?: string
    actionLabel?: string
}

export function CreditAddDialog({
    open,
    onOpenChange,
    defaultUsername = '',
    currentBalance = 0,
    onSubmit,
    title = 'Add Credits',
    actionLabel = 'Add',
}: Props) {
    const form = useForm<CreditFormValues>({
        resolver: zodResolver(creditFormSchema),
        defaultValues: {
            username: defaultUsername,
            amount: '',
        },
    })

    const handleSubmit = (values: CreditFormValues) => {
        onSubmit(values)
        form.reset()
        onOpenChange(false)
    }

    const handleCancel = () => {
        form.reset()
        onOpenChange(false)
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
                    <div className="flex items-center justify-end gap-1 text-sm text-muted-foreground mt-1">
                        <Coins className="w-4 h-4" />
                        <span>Credit / Coin Balance:</span>
                        <span className="font-semibold text-foreground">{currentBalance}</span>
                    </div>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="username"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Username</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter username" {...field} disabled />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="amount"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Credit Amount</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter credit amount" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <DialogFooter className="gap-2">
                            <Button variant="outline" type="button" onClick={handleCancel}>
                                Cancel
                            </Button>
                            <Button type="submit">{actionLabel}</Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}
