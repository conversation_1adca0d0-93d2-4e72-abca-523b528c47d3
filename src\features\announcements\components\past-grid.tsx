import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { formatDateToReadable } from '@/features/members/utils/utilities';

const PostGrid: React.FC<any> = ({ data: posts = [], sentAnnouncementPast }: any) => {

    console.log(posts)

    return (
        <div >
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                {posts.length > 0 && posts.map((post: any, index: any) => (
                    <Card key={index} className="flex flex-col justify-between h-full">
                        <CardContent className="flex flex-col justify-between h-full p-0">
                            <p className="text-sm text-muted-foreground">{post.content}</p>
                            <Separator className="my-4" />
                            <div className="flex justify-between items-center text-xs text-muted-foreground">
                                <span>{formatDateToReadable(post.updatedAt)}</span>
                                <Button onClick={() => sentAnnouncementPast(false, post.id)} style={{ cursor: 'pointer' }} variant="ghost" size="sm" className="text-xs p-0 h-0">
                                    Repost
                                </Button>
                            </div>
                        </CardContent >
                    </Card >
                ))}
            </div >
        </div >
    );
};

export default PostGrid;
