import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Member } from '../../data/schema'
import { CreditAddDialog } from './credit-add-dialog'
import { useState } from 'react'

interface CreditHistoryTableProps {
  member: Member
}

// Mock credit history data - in real app this would come from API
const mockCreditHistory = [
  { id: '1', dateTime: 'Feb 10, 2025 6:02 pm', action: 'Registration free credits', credits: '+126' },
  { id: '2', dateTime: 'Feb 10, 2025 9:48 am', action: 'Registration free credits', credits: '+126' },
  { id: '3', dateTime: 'Jan 31, 2025 10:30 pm', action: 'Registration free credits', credits: '+126' },
  { id: '4', dateTime: 'Feb 15, 2025 12:51 pm', action: 'Registration free credits', credits: '+126' },
  { id: '5', dateTime: 'Feb 2, 2025 1:45 am', action: 'Registration free credits', credits: '+126' },
  { id: '6', dateTime: 'Feb 8, 2025 10:04 am', action: 'Registration free credits', credits: '+126' },
  { id: '7', dateTime: 'Feb 19, 2025 1:55 pm', action: 'Registration free credits', credits: '+126' },
  { id: '8', dateTime: 'Feb 5, 2025 4:02 pm', action: 'Registration free credits', credits: '+126' },
  { id: '9', dateTime: 'Feb 4, 2025 7:09 pm', action: 'Registration free credits', credits: '+126' },
  { id: '10', dateTime: 'Feb 25, 2025 6:56 am', action: 'Registration free credits', credits: '+126' },
  { id: '11', dateTime: 'Jan 31, 2025 12:03 pm', action: 'Registration free credits', credits: '+126' },
  { id: '12', dateTime: 'Feb 1, 2025 6:30 am', action: 'Registration free credits', credits: '+126' },
  { id: '13', dateTime: 'Jan 27, 2025 3:15 am', action: 'Registration free credits', credits: '+126' },
  { id: '14', dateTime: 'Feb 15, 2025 12:23 pm', action: 'Registration free credits', credits: '+126' },
  { id: '15', dateTime: 'Jan 29, 2025 10:35 am', action: 'Registration free credits', credits: '+126' }
]

export default function CreditHistoryTable({ member }: CreditHistoryTableProps) {

  const [open, setOpen] = useState(false)
  const [dialogMode, setDialogMode] = useState<'add' | 'deduct'>('add')

  const handleAddSubmit = (data: any) => {
    console.log('Adding credits:', data)
  }

  const handleDeductSubmit = (data: any) => {
    console.log('Deducting credits:', data)
  }

  return (
    <Card className='p-4 gap-3'>
      <CardHeader className="p-0">
        <div className="flex items-center justify-between">
          <div className='flex gap-2 items-center'>
            <CardTitle className="text-base text-muted-foreground">Credit / Coin Balance:</CardTitle>
            <span className="text-sm font-semibold bg-[#E7E7E7] p-[12px] rounded-full flex gap-1 items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
                <path d="M21.5 17.0815V18.4385C21.5 19.2525 21.119 19.9485 20.59 20.4955C20.067 21.0375 19.357 21.4795 18.558 21.8295C16.956 22.5295 14.814 22.9385 12.5 22.9385C10.186 22.9385 8.044 22.5305 6.442 21.8295C5.643 21.4795 4.933 21.0375 4.41 20.4955C3.925 19.9955 3.565 19.3675 3.508 18.6395L3.5 18.4385V17.0815C3.96467 17.3468 4.46267 17.5785 4.994 17.7765C7.024 18.5275 9.679 18.9465 12.5 18.9465C15.321 18.9465 17.976 18.5275 20.006 17.7765C20.4047 17.6285 20.7843 17.4608 21.145 17.2735L21.5 17.0815ZM3.5 11.5815C3.96467 11.8468 4.46267 12.0785 4.994 12.2765C7.024 13.0275 9.679 13.4465 12.5 13.4465C15.321 13.4465 17.976 13.0275 20.006 12.2765C20.5223 12.0863 21.022 11.8538 21.5 11.5815V14.6865C20.8362 15.1994 20.0985 15.6087 19.312 15.9005C17.557 16.5505 15.148 16.9475 12.5 16.9475C9.853 16.9475 7.444 16.5505 5.688 15.9005C4.90151 15.6087 4.16379 15.1994 3.5 14.6865V11.5815ZM12.5 3.93848C14.814 3.93848 16.956 4.34648 18.558 5.04748C19.357 5.39748 20.067 5.83948 20.59 6.38148C21.075 6.88148 21.435 7.50948 21.492 8.23748L21.5 8.43848V9.18648C20.8362 9.69939 20.0985 10.1087 19.312 10.4005C17.557 11.0505 15.148 11.4475 12.5 11.4475C9.853 11.4475 7.444 11.0505 5.688 10.4005C5.01199 10.1493 4.3716 9.81115 3.783 9.39448L3.5 9.18648V8.43848C3.5 7.62448 3.881 6.92848 4.41 6.38148C4.933 5.83948 5.643 5.39748 6.442 5.04748C8.044 4.34748 10.186 3.93848 12.5 3.93848Z" fill="#4F4F4F" />
              </svg>
              {member.credits}</span>
          </div>
          <div className="flex items-center gap-2">

            <select
              className="bg-[#4F4F4F] text-white p-2 rounded text-sm cursor-pointer"
              onChange={(e) => {
                const value = e.target.value
                if (value === 'add') {
                  setDialogMode('add')
                  setOpen(true)
                } else if (value === 'deduct') {
                  setDialogMode('deduct')
                  setOpen(true)
                }
                e.target.value = ''
              }}
            >
              <option value="" disabled selected>Credit</option>
              <option value="add">Add Credits</option>
              <option value="deduct">Deduct Credit</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-hidden">
          <table className="w-full">
            <thead className="bg-[#4F4F4F] text-white">
              <tr>
                <th className="text-left py-3 px-4 font-medium text-sm">#</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Date/Time</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Action</th>
                <th className="text-right py-3 px-4 font-medium text-sm">Credits</th>
              </tr>
            </thead>
            <tbody>
              {mockCreditHistory.map((transaction, index) => (
                <tr
                  key={transaction.id}
                  className={`border-b border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${index % 2 === 0
                    ? 'bg-white dark:bg-gray-900'
                    : 'bg-gray-50 dark:bg-gray-800'
                    }`}
                >
                  <td className="py-3 px-4 text-sm text-muted-foreground">
                    {index + 1}
                  </td>
                  <td className="py-3 px-4 text-sm">
                    {transaction.dateTime}
                  </td>
                  <td className="py-3 px-4 text-sm">
                    {transaction.action}
                  </td>
                  <td className="py-3 px-4 text-sm text-right">
                    <span className="text-green-600 font-medium">
                      {transaction.credits}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Table Footer with Summary */}
        <div className="p-4 bg-muted/30 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              Showing {mockCreditHistory.length} transactions
            </span>
            <span className="font-medium">
              Total Credits: <span className="text-green-600">+{mockCreditHistory.length * 126}</span>
            </span>
          </div>
        </div>
      </CardContent>

      {/* add credit Modal */}

      <CreditAddDialog
        open={open}
        onOpenChange={setOpen}
        defaultUsername={member.name}
        currentBalance={member.credits}
        title={dialogMode === 'add' ? 'Add Credits' : 'Deduct Credits'}
        actionLabel={dialogMode === 'add' ? 'Add' : 'Deduct'}
        onSubmit={dialogMode === 'add' ? handleAddSubmit : handleDeductSubmit}
      />
    </Card>
  )
}
