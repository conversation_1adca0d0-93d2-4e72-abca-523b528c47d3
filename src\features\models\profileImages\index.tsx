import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState, useRef } from 'react'
import { models } from '../data/models'
import { IconArrowLeft, IconDotsVertical, IconStar, IconTrash, IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
    getPresignedUrl,
    uploadFileToS3,
    addModelProfileImageApi,
    setDefaultModelImageApi,
    deleteModelImageApi,
    getModelProfileImages
} from '../api'
import { validateImageF<PERSON>, S3_BASE_URL } from '@/features/members/utils/utilities'

// Interface for profile images
interface ProfileImage {
    id: string
    filename: string
    url: string
    isDefault: boolean
    uploadedAt: string
}

export default function ModelProfileImages() {
    const { modelId } = useParams({ from: '/_authenticated/models/profile-images/$modelId' })
    const [isUploading, setIsUploading] = useState(false)
    const fileInputRef = useRef<HTMLInputElement>(null)
    const queryClient = useQueryClient()

    // Find the model by ID
    const model = models.find(m => m.id === modelId)

    // API queries and mutations
    const { data: profileImages = [], isLoading } = getModelProfileImages(modelId) as { data: ProfileImage[], isLoading: boolean }
    const { mutateAsync: getPreSignedUrlMutation } = getPresignedUrl()
    const { mutateAsync: uploadFileToS3Mutation } = uploadFileToS3()
    const { mutateAsync: addModelProfileImageMutation } = addModelProfileImageApi()
    const { mutateAsync: setDefaultModelImageMutation } = setDefaultModelImageApi()
    const { mutateAsync: deleteModelImageMutation } = deleteModelImageApi()

    // if (!model) {
    //     return (
    //         <div className="flex items-center justify-center h-64">
    //             <p className="text-muted-foreground">Model not found</p>
    //         </div>
    //     )
    // }

    const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (!file) return

        const error = validateImageFile(file)
        if (error) {
            toast.error(error)
            return
        }

        setIsUploading(true)
        try {
            const ext = file.name.split(".").pop()?.toLowerCase() || "jpg"
            const presignedRes: any = await getPreSignedUrlMutation({
                location: "models",
                type: ext,
                count: 1,
            })
            const fileData = presignedRes?.data?.files?.[0]
            if (!fileData) {
                toast.error("Failed to get S3 upload URL.")
                return
            }
            await uploadFileToS3Mutation({
                url: fileData.url,
                file,
            })

            // Add the image to the model's profile images
            await addModelProfileImageMutation({
                modelId: modelId,
                imagePath: fileData.filename
            })

            toast.success("Image uploaded successfully!")
            queryClient.invalidateQueries({ queryKey: ['model-profile-images'] })

        } catch (err: any) {
            console.log(err)
            toast.error("Failed to upload image")
        } finally {
            setIsUploading(false)
            e.target.value = ""
        }
    }

    const handleSetAsDefault = async (imageId: string) => {
        try {
            await setDefaultModelImageMutation({
                modelId: modelId,
                imageId: imageId
            })
            toast.success("Set as default cover successfully!")
            queryClient.invalidateQueries({ queryKey: ['model-profile-images'] })

        } catch (err) {
            toast.error("Failed to set as default cover")
        }
    }

    const handleDeleteImage = async (imageId: string) => {
        try {
            await deleteModelImageMutation({
                modelId: modelId,
                imageId: imageId
            })
            toast.success("Image deleted successfully!")
            queryClient.invalidateQueries({ queryKey: ['model-profile-images'] })

        } catch (err) {
            toast.error("Failed to delete image")
        }
    }

    const handleUploadClick = () => {
        fileInputRef.current?.click()
    }

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
                    <Link to="/models" className="flex items-center gap-1 hover:text-gray-900">
                        <IconArrowLeft className="h-4 w-4" />
                        Models
                    </Link>
                    <span>/</span>
                    <Link to="/models/profile/$modelId" params={{ modelId }} className="hover:text-gray-900">
                        {model?.profile}
                    </Link>
                    <span>/</span>
                    <span className="text-gray-900">Profile Images</span>
                </div>

                {/* Main Content */}
                <div className="space-y-6">
                    {/* Upload Section */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Upload Profile Image</CardTitle>
                            <p className="text-sm text-muted-foreground">
                                Add new profile images for {model?.profile}
                            </p>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-4">
                                {/* Upload Button */}
                                <div
                                    className="w-20 h-20 flex justify-center items-center border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors bg-gray-50"
                                    onClick={handleUploadClick}
                                >
                                    {isUploading ? (
                                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                    ) : (
                                        <IconPlus className="h-6 w-6 text-gray-400" />
                                    )}
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Smiley Image File</p>
                                    <p className="text-xs text-muted-foreground">Click the + icon to upload an image</p>
                                </div>
                                <Input
                                    type="file"
                                    ref={fileInputRef}
                                    className="hidden"
                                    onChange={handleImageUpload}
                                    accept="image/*"
                                    disabled={isUploading}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Profile Images List */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Profile Images</CardTitle>
                            <p className="text-sm text-muted-foreground">
                                Manage {model?.profile}'s profile images
                            </p>
                        </CardHeader>
                        <CardContent>
                            {isLoading ? (
                                <div className="text-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                                    <p className="text-muted-foreground mt-2">Loading images...</p>
                                </div>
                            ) : profileImages.length === 0 ? (
                                <div className="text-center py-8">
                                    <p className="text-muted-foreground">No profile images uploaded yet</p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                                    {profileImages.map((image) => (
                                        <div key={image.id} className="relative group">
                                            {/* Image Container */}
                                            <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100">
                                                <img
                                                    src={image.url || S3_BASE_URL + image.filename}
                                                    alt={`Profile ${image.id}`}
                                                    className="w-full h-full object-cover"
                                                    loading="lazy"
                                                />

                                                {/* Default Badge */}
                                                {image.isDefault && (
                                                    <div className="absolute top-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded flex items-center gap-1">
                                                        <IconStar className="h-3 w-3" />
                                                        Default
                                                    </div>
                                                )}

                                                {/* Menu Button */}
                                                <div className="absolute top-2 right-2">
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button
                                                                variant="secondary"
                                                                size="icon"
                                                                className="h-8 w-8 bg-white/80 hover:bg-white/90 backdrop-blur-sm"
                                                            >
                                                                <IconDotsVertical className="h-4 w-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end" className="w-48">
                                                            {!image.isDefault && (
                                                                <>
                                                                    <DropdownMenuItem
                                                                        onClick={() => handleSetAsDefault(image.id)}
                                                                        className="flex items-center gap-2"
                                                                    >
                                                                        <IconStar className="h-4 w-4" />
                                                                        Set as Default Cover
                                                                    </DropdownMenuItem>
                                                                    <DropdownMenuSeparator />
                                                                </>
                                                            )}
                                                            <DropdownMenuItem
                                                                onClick={() => handleDeleteImage(image.id)}
                                                                className="flex items-center gap-2 text-destructive focus:text-destructive"
                                                            >
                                                                <IconTrash className="h-4 w-4" />
                                                                Delete Image
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </div>
                                            </div>

                                            {/* Image Info */}
                                            <div className="mt-2 text-xs text-muted-foreground">
                                                <p className="truncate">{image.filename}</p>
                                                <p>{new Date(image.uploadedAt).toLocaleDateString()}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}