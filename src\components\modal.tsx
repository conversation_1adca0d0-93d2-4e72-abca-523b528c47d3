import { cn } from '@/lib/utils'
import {
    AlertDialog,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { IconDatabase, IconHeart, IconStack2, IconUser } from '@tabler/icons-react'

interface ConfirmDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    title: React.ReactNode
    disabled?: boolean
    desc: React.JSX.Element | string
    cancelBtnText?: string
    confirmText?: React.ReactNode
    destructive?: boolean
    handleConfirm: () => void
    isLoading?: boolean
    className?: string
    children?: React.ReactNode
}

export function ConfirmDialog(props: ConfirmDialogProps) {
    const {
        title,
        desc,
        children,
        className,
        confirmText,
        cancelBtnText,
        destructive,
        isLoading,
        disabled = false,
        handleConfirm,
        ...actions
    } = props
    return (
        <AlertDialog {...actions} >
            <AlertDialogContent width="max-w-[1250px]" className={cn("p-0 radius-md overflow-hidden bg-card", className && className)}>
                <div className="p-6 bg-muted/50">
                    <h2 className="text-2xl font-semibold mb-6">Client Profile</h2>
                    <div className="grid grid-cols-1 lg:grid-cols-[400px_1fr] gap-4">
                        {/* Left Profile Section */}
                        <div className="max-w-sm">
                            <h2 className="text-lg font-semibold text-foreground mb-3">About Client</h2>

                            <div className="flex flex-col gap-1 max-h-[80dvh] overflow-y-auto">
                                {/* Profile Header */}
                                <div className="bg-gray-100 rounded-xl p-4">
                                    <div className="flex justify-between items-start">
                                        <div className="w-14 h-14 bg-gray-300 rounded-full" />
                                        <button className="text-gray-400 hover:text-gray-600">
                                            <IconHeart size={20} />
                                        </button>
                                    </div>

                                    {/* Name and Age */}
                                    <div className='flex items-center justify-between mt-3'>
                                        <div className="font-semibold text-gray-800 text-lg">
                                            Edward65 <span>(66)</span>
                                        </div>
                                        <div className="flex items-center gap-1 bg-gray-100 text-sm text-gray-600">
                                            <IconStack2 size={16} />
                                            2491
                                        </div>
                                    </div>


                                    {/* Last Active and Timezone */}
                                    <div className="text-sm text-gray-500">
                                        Last Active: 11 Sep 2024, 1:45 PM
                                    </div>
                                    <div className="text-sm text-gray-500">Timezone: GMT+5:30</div>

                                    {/* Description */}
                                    <p className="text-sm text-gray-500 mt-2 leading-snug">
                                        Maecenas dignissim justo eget nulla rutrum molestie. Maecenas lobortis
                                        sem dui, vel rutrum. Vivamus sed libero ornare, tristique quam in.
                                    </p>
                                </div>

                                {/* Section: Personal Details */}
                                <div className="bg-gray-100 rounded-xl p-4">
                                    <div className="font-semibold flex items-center gap-1">
                                        <IconUser size={16} /> Personal Details
                                    </div>
                                    <hr className='my-2'></hr>
                                    <div className="grid grid-cols-2 gap-y-2 text-sm">
                                        <div>
                                            <div className="text-gray-500">Gender</div>
                                            <div>Female</div>
                                        </div>
                                        <div>
                                            <div className="text-gray-500">Relation Status</div>
                                            <div>Married</div>
                                        </div>
                                        <div>
                                            <div className="text-gray-500">Personality</div>
                                            <div>Extrovert</div>
                                        </div>
                                        <div>
                                            <div className="text-gray-500">Location</div>
                                            <div>City of Westminster,<br />United Kingdom</div>
                                        </div>
                                    </div>
                                </div>

                                {/* Section: Physical Attributes */}
                                <div className="bg-muted/50 rounded-xl p-4">
                                    <div className="font-semibold flex items-center gap-1">
                                        <IconUser size={16} /> Physical Attributes
                                    </div>
                                    <hr className='my-2'></hr>
                                    <div className="grid grid-cols-2 gap-y-2 text-sm">
                                        <div>
                                            <div className="text-muted-foreground">Hair Color</div>
                                            <div>Blond</div>
                                        </div>
                                        <div>
                                            <div className="text-muted-foreground">Eye Color</div>
                                            <div>Grey</div>
                                        </div>
                                        <div>
                                            <div className="text-muted-foreground">Height</div>
                                            <div>168 cm</div>
                                        </div>
                                        <div>
                                            <div className="text-muted-foreground">Appearance</div>
                                            <div>Good enough</div>
                                        </div>
                                        <div>
                                            <div className="text-muted-foreground">Body Type</div>
                                            <div>Average</div>
                                        </div>
                                    </div>
                                </div>

                                {/* Section: Habits */}
                                <div className="bg-muted/50 rounded-xl p-4">
                                    <div className="font-semibold flex items-center gap-1">
                                        <IconUser size={16} /> Habits
                                    </div>
                                    <hr className='my-2'></hr>
                                    <div className="grid grid-cols-2 gap-y-2 text-sm">
                                        <div>
                                            <div className="text-muted-foreground">Smoking</div>
                                            <div>Socially</div>
                                        </div>
                                        <div>
                                            <div className="text-muted-foreground">Drinking</div>
                                            <div>Socially</div>
                                        </div>
                                    </div>
                                </div>

                                {/* Section: Lifestyle and Preferences (Placeholder for future) */}
                                <div className="bg-muted/50 rounded-xl p-4">
                                    <div className="font-semibold mb-2 flex items-center gap-1">
                                        <IconUser size={16} /> Lifestyle and Preferences
                                    </div>
                                    <hr className='my-2'></hr>
                                    <div className="grid grid-cols-2 gap-y-2 text-sm">
                                        <div>
                                            <div className="text-muted-foreground">Smoking</div>
                                            <div>Socially</div>
                                        </div>
                                        <div>
                                            <div className="text-muted-foreground">Drinking</div>
                                            <div>Socially</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right Gallery Section */}
                        <div className="">
                            <h2 className="text-lg font-semibold text-foreground mb-3">Gallery</h2>
                            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-5 max-h-[80dvh] overflow-y-auto">
                                {Array.from({ length: 9 }).map((_, index) => (
                                    <div
                                        key={index}
                                        className="aspect-square bg-muted rounded-lg"
                                    />
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </AlertDialogContent>
        </AlertDialog>
    )
}
