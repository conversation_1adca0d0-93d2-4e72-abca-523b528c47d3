// PaginationControls.tsx
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'

function getPageButtons(current: number, total: number): (number | string)[] {
    const delta = 2
    const range: (number | string)[] = []

    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
    }

    if (current - delta > 2) range.unshift('...')
    if (current + delta < total - 1) range.push('...')

    range.unshift(1)
    if (total > 1) range.push(total)

    return range
}

interface PaginationControlsProps<T> {
    table: Table<T>
    meta?: {
        total: number
        page: number
        pages: number
        limit: number
    }
    onPageChange?: any
}

export function PaginationControls<T>({ table, meta, onPageChange }: PaginationControlsProps<T>) {
    if (!meta) return null

    const pageIndex = table.getState().pagination.pageIndex
    const pageCount = meta.pages
    const currentPage = pageIndex + 1
    const pages = getPageButtons(currentPage, pageCount)
    return (
        <div className="flex justify-center items-center space-x-1 mt-4 flex-wrap">
            <Button
                variant="outline"
                size="sm"
                onClick={() => {
                    table.setPageIndex(0)
                    onPageChange(0)
                }}
                disabled={currentPage === 1}
                className='bg-card'
            >
                « First
            </Button>
            <Button
                variant="outline"
                size="sm"
                onClick={() => {
                    table.previousPage()
                    onPageChange(currentPage - 2)
                }}
                disabled={currentPage === 1}
                className='bg-card'
            >
                ‹ Back
            </Button>

            {pages?.map((page, index) =>
                typeof page === 'string' ? (
                    <span key={index} className="px-2 text-muted-foreground">
                        {page}
                    </span>
                ) : (
                    <Button
                        key={index}
                        variant={page === currentPage ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => {
                            table.setPageIndex(page - 1)
                            onPageChange(page - 1)
                        }}
                        className={page === currentPage ? 'default' : 'bg-card'}
                    >
                        {page}
                    </Button>
                )
            )}

            <Button
                variant="outline"
                size="sm"
                onClick={() => {
                    table.nextPage()
                    onPageChange(currentPage)
                }}
                disabled={currentPage === pageCount}
                className='bg-card'
            >
                Next ›
            </Button>
            <Button
                variant="outline"
                size="sm"
                onClick={() => {
                    table.setPageIndex(pageCount - 1)
                    onPageChange(pageCount - 1)
                }}
                disabled={currentPage === pageCount}
                className='bg-card'
            >
                Last »
            </Button>
        </div>
    )
}
