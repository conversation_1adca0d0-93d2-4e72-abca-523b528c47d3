import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent } from '@/components/ui/tabs'
import { StatCard } from './components/stat-card'
import StatisticsChart from './components/bars'
import {
  DashboardTable,
  countryDomainColumns,
  affiliateColumns,
  signupColumns,
  salesColumns,
} from './components/dashboard-table'
import {
  countryDomainData,
  affiliateData,
  signupData,
  salesData,
  wlAffiliateData,
} from './data/sample-data'
import { useAuthStore } from '@/stores/authStore'

const dummyData = [
  { value: 2000 },
  { value: 4000 },
  { value: 3000 },
  { value: 6000 },
  { value: 3200 },
  { value: 5000 },
  { value: 6000 },
]

export default function Dashboard() {
  const { user } = useAuthStore(state => state.auth)
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Hello, {user?.name}</h1>
          {/* <div className='flex items-center space-x-2'>
            <Button>Download</Button>
          </div> */}
        </div>
        <Tabs orientation='vertical' defaultValue='overview' className='space-y-4'>
          <TabsContent value='overview' className='space-y-4'>
            <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-3'>
              <StatCard
                title='Total Revenue'
                value='$45,231.89'
                percentage='+20.1%'
                isPositive={true}
                data={dummyData}
              />
              <StatCard
                title='Signups'
                value='+2350'
                percentage='+180.1%'
                isPositive={true}
                data={dummyData}
              />
              <StatCard
                title='Messages'
                value='+12,234'
                percentage='+19%'
                isPositive={true}
                data={dummyData}
              />
            </div>
            {/* <div className='grid grid-cols-1 gap-4 lg:grid-cols-7'>
              <Card className='col-span-1 lg:col-span-4'>
                <CardHeader>
                  <CardTitle>Overview</CardTitle>
                </CardHeader>
                <CardContent className='pl-2'>
                  <Overview />
                </CardContent>
              </Card>
              <Card className='col-span-1 lg:col-span-3'>
                <CardHeader>
                  <CardTitle>Recent Sales</CardTitle>
                  <p className='text-muted-foreground text-sm'>You made 265 sales this month.</p>
                </CardHeader>
                <CardContent>
                  <RecentSales />
                </CardContent>
              </Card>
            </div> */}


            {/* Dashboard Tables */}
            <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
              {/* Country/Domain Statistics */}
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-base font-medium'>Country/Domain Statistic</CardTitle>
                  <div className='flex items-center space-x-2'>
                    <span className='text-sm text-muted-foreground'>Countries</span>
                    <Button variant='ghost' size='sm' className='h-6 w-6 p-0'>
                      <span className='sr-only'>More options</span>
                      <svg className='h-4 w-4' fill='currentColor' viewBox='0 0 20 20'>
                        <path d='M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z' />
                      </svg>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <DashboardTable
                    data={countryDomainData}
                    columns={countryDomainColumns}
                    title='Country/Domain Statistics'
                    showToolbar={false}
                    showPagination={false}
                    pageSize={10}
                  />
                </CardContent>
              </Card>

              {/* Top 10 Affiliates */}
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-base font-medium'>Top 10 Affiliates</CardTitle>
                  <div className='flex items-center space-x-2'>
                    <Button variant='ghost' size='sm' className='h-6 w-6 p-0'>
                      <span className='sr-only'>More options</span>
                      <svg className='h-4 w-4' fill='currentColor' viewBox='0 0 20 20'>
                        <path d='M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z' />
                      </svg>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <DashboardTable
                    data={affiliateData}
                    columns={affiliateColumns}
                    title='Top 10 Affiliates'
                    showToolbar={false}
                    showPagination={false}
                    pageSize={10}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Bottom Tables Row */}
            <div className='grid grid-cols-1 gap-4 lg:grid-cols-3'>
              {/* Signups */}
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-base font-medium'>Signups (Top 5 domains)</CardTitle>
                  <div className='flex items-center space-x-2'>
                    <Button variant='ghost' size='sm' className='h-6 w-6 p-0'>
                      <span className='sr-only'>More options</span>
                      <svg className='h-4 w-4' fill='currentColor' viewBox='0 0 20 20'>
                        <path d='M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z' />
                      </svg>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <DashboardTable
                    data={signupData.slice(0, 5)}
                    columns={signupColumns}
                    title='Signups'
                    showToolbar={false}
                    showPagination={false}
                    pageSize={5}
                  />
                </CardContent>
              </Card>

              {/* Sales */}
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-base font-medium'>Sales (Top 5 domains)</CardTitle>
                  <div className='flex items-center space-x-2'>
                    <Button variant='ghost' size='sm' className='h-6 w-6 p-0'>
                      <span className='sr-only'>More options</span>
                      <svg className='h-4 w-4' fill='currentColor' viewBox='0 0 20 20'>
                        <path d='M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z' />
                      </svg>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <DashboardTable
                    data={salesData.slice(0, 5)}
                    columns={salesColumns}
                    title='Sales'
                    showToolbar={false}
                    showPagination={false}
                    pageSize={5}
                  />
                </CardContent>
              </Card>

              {/* WL Affiliates */}
              <Card>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-base font-medium'>Sales (Top 5 WL Affiliates)</CardTitle>
                  <div className='flex items-center space-x-2'>
                    <Button variant='ghost' size='sm' className='h-6 w-6 p-0'>
                      <span className='sr-only'>More options</span>
                      <svg className='h-4 w-4' fill='currentColor' viewBox='0 0 20 20'>
                        <path d='M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z' />
                      </svg>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <DashboardTable
                    data={wlAffiliateData.slice(0, 5)}
                    columns={salesColumns}
                    title='WL Affiliates'
                    showToolbar={false}
                    showPagination={false}
                    pageSize={5}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <div className='mt-4'>
          <StatisticsChart />
        </div>
      </Main>
    </>
  )
}

const topNav: any = [
  // {
  //   title: 'Overview',
  //   href: 'dashboard/overview',
  //   isActive: true,
  //   disabled: false,
  // },
  // {
  //   title: 'Customers',
  //   href: 'dashboard/customers',
  //   isActive: false,
  //   disabled: true,
  // },
  // {
  //   title: 'Products',
  //   href: 'dashboard/products',
  //   isActive: false,
  //   disabled: true,
  // },
  // {
  //   title: 'Settings',
  //   href: 'dashboard/settings',
  //   isActive: false,
  //   disabled: true,
  // },
]
