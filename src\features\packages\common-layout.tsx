import { Header } from "@/components/layout/header";
import { ProfileDropdown } from "@/components/profile-dropdown";
import { ThemeSwitch } from "@/components/theme-switch";
import { Outlet } from "@tanstack/react-router";

export default function CommonLayout() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className="ml-auto flex items-center space-x-4 ">
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Outlet />
    </>
  );
}
