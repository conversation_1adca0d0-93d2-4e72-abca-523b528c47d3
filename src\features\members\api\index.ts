import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";
import axios from "axios";


export const useGetMembers = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.MEMBERS, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["members-list"],
    });



export const updateMemberlApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.put(`${API_ENDPOINTS.MEMBERS}/${payload?.id}`, payload);
        },
    });

export const getMemberDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.MEMBERS}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}

        },
        queryKey: ["member-details", id],
        enabled: !!id
    });

export const getMasterApi = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.MASTER);
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["country-list"],
        staleTime: 0
    });

export const getCountryList = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.COUNTRY_LIST);
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["master-list"],
    });

export const getPresignedUrl = () =>
    useMutation({
        mutationFn: async (params: any) => {
            return await apiClient.get(`${API_ENDPOINTS.S3_PRESIGNED_URL}`, { params });
        },
    });

export const updateAvatarApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.put(`${API_ENDPOINTS.MEMBERS}/${payload?.id}/avatar`, { imagePath: payload.imagePath });
        },
    });

export const uploadFileToS3 = () =>
    useMutation({
        mutationFn: async ({ url, file }: any) => {
            return await axios.put(url, file);
        },
    });

export const memberStatusChange = () =>
    useMutation({
        mutationFn: async (id: any) => {
            return await apiClient.get(`${API_ENDPOINTS.MEMBERS}/${id}/update-status`);
        },
    });

export const deleteMemberApi = () =>
    useMutation({
        mutationFn: async (id: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.MEMBERS}/${id}`);
        },
    });

export const imageApprovalApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.get(`${API_ENDPOINTS.MEMBERS}/${payload?.id}/avatar-approval`);
        },
    });
