import { getAccessToken } from "@/stores/authStore";
import axios, { AxiosResponse, InternalAxiosRequestConfig } from "axios";
import { toast } from "sonner";
import { useAuthStore } from "@/stores/authStore";


export const apiClient = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    withCredentials: true,
    headers: {
        "Accept-Language": "en",
        "x-redsoft-platform": "web",
        "x-redsoft-version": "1.0.0",
    },
});

const authRequestInterceptor = (config: InternalAxiosRequestConfig) => {
    const token = getAccessToken()

    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
};

apiClient.interceptors.request.use(authRequestInterceptor);

apiClient.interceptors.response.use(
    (response: AxiosResponse) => {
        if (!response?.data?.success) {
            response?.data?.message
                ? toast.error(response?.data?.message)
                : null;
        }
        return response.data;
    },
    (error) => {

        if (error.response && error.response.status === 401) {
            useAuthStore.getState().auth.reset();
            return error?.response?.data
        }
        if (typeof error.response === "undefined") {
            toast.error("A network error occurred. Please try again after some time.");
            return;
        }

        toast.error(error?.response?.data?.message || "An error occurred, please try again.");
        return Promise.reject(error);
    }
);

