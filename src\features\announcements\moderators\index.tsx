import { CONSTANTS } from "@/features/members/utils/constant";
import { getActiveAnnouncementsApi, getAllAnnouncementsApi, setPastAnnouncement } from "../api";
import { AnnouncementCard } from "../components/active-announcements";
import PostGrid from "../components/past-grid";

export default function ModeratorsComponent() {
    const { data: { announcements: moderatorAnnouncements = [] } = {}, refetch } = getActiveAnnouncementsApi({ type: CONSTANTS.MODERATOR });
    const { mutateAsync: setPastAnnouncementMutation } = setPastAnnouncement();
    const { data: { announcement: moderatorAllAnnouncements = [] } = {}, refetch: refetchAll } = getAllAnnouncementsApi({ type: CONSTANTS.MODERATOR });

    const sentAnnouncementPast = async (_isChecked: boolean, id: any) => {
        try {
            const response: any = await setPastAnnouncementMutation(id);
            refetch();
            refetchAll();
        } catch (error: any) {
            console.log(error);
        }
    };

    return (
        <div className="p-6 space-y-6">
            <div className="flex gap-4">
                {moderatorAnnouncements.map((item: any) => (
                    <AnnouncementCard
                        key={item.id}
                        name={item.name}
                        email={item.email}
                        content={item.content}
                        date={item.updatedAt}
                        id={item.id}
                        defaultIsPast={item.isPast}
                        onToggleChange={sentAnnouncementPast}
                    />
                ))}
            </div>

            <div className="space-y-2">
                {moderatorAllAnnouncements?.length > 0 &&
                    <>
                        <label className="font-medium">Past Moderator Announcements</label>
                        <PostGrid data={moderatorAllAnnouncements} sentAnnouncementPast={sentAnnouncementPast} />
                    </>
                }
            </div>
        </div>
    );
} 