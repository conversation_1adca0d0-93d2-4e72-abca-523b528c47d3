"use client";

import { Main } from "@/components/layout/main";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { Separator } from "@radix-ui/react-separator";
import { useForm } from "react-hook-form";
import { basicInformationSchema, BasicInfoValues } from "../data/settings";
import { getBasicSettings, updateBasicSettings } from "../api";
import { toast } from "sonner";
import { useEffect } from "react";


export default function BasicInformationSetting() {
    const { data } = getBasicSettings()
    const { mutateAsync: updateBasicSettingMutation } = updateBasicSettings()

    const form = useForm<any>({
        resolver: zodResolver(basicInformationSchema),
        defaultValues: {
            chatType: data?.setting?.chatType || "PRIVATE",
            minChatLetters: data?.setting?.minChatLetters || 0,
            chatIdleTime: data?.setting?.chatIdleTime || 0,
            moderatorLoginIdleTime: data?.setting?.moderatorLoginIdleTime || 0,
            platformBotMessageOn: data?.setting?.platformBotMessageOn || false,
            botMessageInterval: data?.setting?.botMessageInterval || 0,
            botMessagesForNewRegistered: data?.setting?.botMessagesForNewRegistered || "",
            platformBotViewOn: data?.setting?.platformBotViewOn || false,
            botViewInterval: data?.setting?.botViewInterval || 0,
            botViewsForNewRegistered: data?.setting?.botViewsForNewRegistered || 0,
            messageSendToHold: data?.setting?.messageSendToHold || "",
            companyName: data?.setting?.companyName || "",
            companyEmail: data?.setting?.companyEmail || "",
            companyAddress: data?.setting?.companyAddress || "",
            companyVatNo: data?.setting?.companyVatNo || 0,
            generalCommissionAmount: data?.setting?.generalCommissionAmount || 0,
            reLobbyCommissionAmount: data?.setting?.reLobbyCommissionAmount || 0
        } as BasicInfoValues,
    });

    const { control, handleSubmit, setValue, reset } = form;

    const onSubmit = async (values: BasicInfoValues) => {
        const response: any = await updateBasicSettingMutation({ ...values, id: data.setting.id })
        if (response?.success) {
            toast.success("Setting successfully updated!")
        }
    };

    useEffect(() => {
        if (data?.setting) {
            reset(data.setting)
        }
    }, [data?.setting, reset, setValue])

    return (
        <Main className="p-0">
            <div className="space-y-2">
                <Separator />
                <Form {...form}>
                    <form
                        onSubmit={handleSubmit(onSubmit)}
                        className="grid grid-cols-1 xl:grid-cols-2 gap-6"
                    >
                        {/* Basic Details */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Basic Details</CardTitle>
                            </CardHeader>
                            <CardContent className="grid xl:grid-cols-2 gap-4 ">
                                <FormField
                                    name="chatType"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Type of take chat</FormLabel>
                                            <FormControl>
                                                <Select
                                                    value={field.value}
                                                    onValueChange={field.onChange}
                                                >
                                                    <SelectTrigger className="w-full">
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem disabled value="_">Select</SelectItem>
                                                        <SelectItem value="PRIVATE">Send auto chat to moderator</SelectItem>
                                                        <SelectItem value="GROUP">Send Moderator on chat list page</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />



                                <FormField
                                    name="minChatLetters"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Minimum chat letters</FormLabel>
                                            <FormControl>
                                                <Input type="number" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    name="chatIdleTime"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Chat Idle Time (Sec)</FormLabel>
                                            <FormControl>
                                                <Input type="number" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    name="moderatorLoginIdleTime"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Moderator Logout Idle Time</FormLabel>
                                            <FormControl>
                                                <Input type="number" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />


                                <FormField
                                    name="platformBotMessageOn"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="col-span-2">
                                            <FormLabel>Platform Bot Message On?</FormLabel>
                                            <FormControl>
                                                <RadioGroup
                                                    className="flex gap-4"
                                                    value={field.value}
                                                    onValueChange={field.onChange}
                                                >
                                                    <div className="flex items-center gap-2">
                                                        <RadioGroupItem value={true} id="bot-msg-yes" />
                                                        <label htmlFor="bot-msg-yes">Yes</label>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <RadioGroupItem value={false} id="bot-msg-no" />
                                                        <label htmlFor="bot-msg-no">No</label>
                                                    </div>
                                                </RadioGroup>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    name="botMessageInterval"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Bot Message Interval (In Hours)</FormLabel>
                                            <FormControl>
                                                <Input type="number" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />


                                <FormField
                                    name="botMessagesForNewRegistered"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Bot Messages for New Registered</FormLabel>
                                            <FormControl>
                                                <Input type="number" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    name="platformBotViewOn"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="col-span-2">
                                            <FormLabel>Platform Bot View On?</FormLabel>
                                            <FormControl>
                                                <RadioGroup
                                                    className="flex gap-4"
                                                    value={field.value}
                                                    onValueChange={field.onChange}
                                                >
                                                    <div className="flex items-center gap-2">
                                                        <RadioGroupItem value={true} id="bot-view-yes" />
                                                        <label htmlFor="bot-view-yes">Yes</label>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <RadioGroupItem value={false} id="bot-view-no" />
                                                        <label htmlFor="bot-view-no">No</label>
                                                    </div>
                                                </RadioGroup>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />


                                <FormField
                                    name="botViewInterval"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Bot View Interval (In Hours)</FormLabel>
                                            <FormControl>
                                                <Input type="number" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    name="botViewsForNewRegistered"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Bot Views for New Registered</FormLabel>
                                            <FormControl>
                                                <Input type="number" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    name="messageSendToHold"
                                    control={control}
                                    render={({ field }) => (
                                        <FormItem className="sm:col-span-1 col-span-2">
                                            <FormLabel>Message Send to Hold</FormLabel>
                                            <FormControl>
                                                <Input {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </CardContent>
                        </Card>

                        {/* Company Info */}
                        <div>
                            <Card>
                                <CardHeader>
                                    <CardTitle>Company Information</CardTitle>
                                </CardHeader>
                                <CardContent className="grid grid-cols-1 gap-4">
                                    <FormField
                                        name="companyName"
                                        control={control}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Company Name</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        name="companyEmail"
                                        control={control}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Company Email</FormLabel>
                                                <FormControl>
                                                    <Input {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        name="companyAddress"
                                        control={control}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Company Address</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />

                                    <FormField
                                        name="companyVatNo"
                                        control={control}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Company VAT No.</FormLabel>
                                                <FormControl>
                                                    <Input type="number" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </CardContent>
                            </Card>

                            {/* Commission Info */}
                            <Card className="mt-5">
                                <CardHeader>
                                    <CardTitle>Commission Information</CardTitle>
                                </CardHeader>
                                <CardContent className="grid grid-cols-1 gap-4">
                                    <FormField
                                        name="generalCommissionAmount"
                                        control={control}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>General Commission Amount(€)</FormLabel>
                                                <FormControl>
                                                    <Input type="number" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        name="reLobbyCommissionAmount"
                                        control={control}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Re Lobby Commission Amount(€)</FormLabel>
                                                <FormControl>
                                                    <Input type="number" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </CardContent>
                            </Card>
                        </div>

                        {/* Actions */}
                        <div className="col-span-full flex justify-end gap-4">
                            <Button type="submit">Save</Button>
                        </div>
                    </form>
                </Form>
            </div>
        </Main>
    );
}
