import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useState } from 'react'

interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
  readonly onFilterChanged?: any
}

const domainOptions = [
  'Fashion',
  'Fitness',
  'Adult',
  'Beauty',
  'Travel',
  'Other'
]

const countryOptions = [
  'USA',
  'Canada',
  'United Kingdom',
  'Australia',
  'Germany',
  'France',
  'Brazil',
  'Mexico',
  'Portugal',
  'Spain',
  "India"
]

const ageRangeOptions = [
  '18-25',
  '26-30',
  '31-35',
  '36-40',
  '41-45',
  '46-50',
  '50+'
]

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  const [filters, setFilters] = useState({
    domain: undefined as string | undefined,
    country: undefined as string | undefined,
    ageRange: undefined as string | undefined,
  })

  const [hasSearched, setHasSearched] = useState(false)

  const handleFilterChange = (
    key: 'domain' | 'country' | 'ageRange',
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleSearch = () => {
    // Apply filters to the table
    if (filters.domain) {
      table.getColumn('domainType')?.setFilterValue(filters.domain)
    }
    if (filters.country) {
      table.getColumn('countryCity')?.setFilterValue(filters.country)
    }
    if (filters.ageRange) {
      // Create a custom filter function for age range
      const ageRangeFilter = {
        filterFn: (row: any, columnId: string, filterValue: string) => {
          const profile = row.getValue(columnId) as string

          // Extract age from profile string like "Alice Johnson (31)"
          const ageRegex = /\((\d+)\)/
          const ageMatch = ageRegex.exec(profile)
          if (!ageMatch) return false

          const userAge = parseInt(ageMatch[1], 10)

          // Parse the age range filter value like "26-30" or "50+"
          if (filterValue.includes('+')) {
            const minAge = parseInt(filterValue.replace('+', ''), 10)
            return userAge >= minAge
          } else if (filterValue.includes('-')) {
            const [minAge, maxAge] = filterValue.split('-').map(age => parseInt(age, 10))
            return userAge >= minAge && userAge <= maxAge
          }

          return false
        }
      }

      // Apply the age range filter to the profile column
      const profileColumn = table.getColumn('profile')
      if (profileColumn) {
        // Set the custom filter function
        profileColumn.columnDef.filterFn = ageRangeFilter.filterFn
        // Apply the filter value
        profileColumn.setFilterValue(filters.ageRange)
      }
    }
    setHasSearched(true)
    onFilterChanged(filters, 1)
  }

  const handleReset = () => {
    // Reset all column filters
    table.resetColumnFilters()

    // Reset the custom filter function for profile column
    const profileColumn = table.getColumn('profile')
    if (profileColumn?.columnDef) {
      // Reset to default filter function (contains text search)
      profileColumn.columnDef.filterFn = 'includesString'
    }

    const f: any = {
      domain: undefined,
      country: undefined,
      ageRange: undefined,
    }
    // Reset filter state
    setFilters(f)
    setHasSearched(false)
    onFilterChanged(f, 0)
  }

  const hasActiveFilters = Boolean(filters.domain ?? filters.country ?? filters.ageRange)

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center gap-4 flex-wrap'>
        <FilterSelect
          value={filters.domain}
          placeholder="Select domain"
          options={domainOptions}
          onChange={(value) => handleFilterChange('domain', value)}
        />
        <FilterSelect
          value={filters.country}
          placeholder="Select country"
          options={countryOptions}
          onChange={(value) => handleFilterChange('country', value)}
        />
        <FilterSelect
          value={filters.ageRange}
          placeholder="Select age range"
          options={ageRangeOptions}
          onChange={(value) => handleFilterChange('ageRange', value)}
        />
        <Button
          onClick={handleSearch}
          className="h-9 px-3"
          disabled={!hasActiveFilters}
        >
          Search
        </Button>
        {(isFiltered || hasSearched) && (
          <Button
            variant='outline'
            onClick={handleReset}
            className='h-9 px-2 lg:px-3'
          >
            Reset
            {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
          </Button>
        )}
      </div>
    </div>
  )
}
