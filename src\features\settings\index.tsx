import { useState } from "react";
import BasicInformationSetting from "./components/BasicInfo";
import PageContentEditor from "./page-content";

export default function TabbedView() {
  const [activeTab, setActiveTab] = useState<"basic" | "page">("basic");

  return (
    <div className="p-4 bg-background min-h-screen">
      {/* Tabs container: white background, centered, rounded, inline only */}
      <div
        id="announcement_tab"
        className="inline-flex rounded-md px-2 py-1 mb-4"
      >
        <button
          style={{ cursor: 'pointer' }}
          className={`px-4 py-2 text-sm ${activeTab === "basic"
            ? "border-b-2 border-black font-medium text-foreground"
            : "text-muted-foreground"
            }`}
          onClick={() => setActiveTab("basic")}
        >
          Basic Informations
        </button>
        <button
          style={{ cursor: 'pointer' }}
          className={`px-4 py-2 text-sm ml-4 ${activeTab === "page"
            ? "border-b-2 border-black font-medium text-foreground"
            : "text-muted-foreground"
            }`}
          onClick={() => setActiveTab("page")}
        >
          Page Contents
        </button>
      </div>

      {/* Tab content area */}
      {activeTab === "basic" ? (
        <div className="text-card-foreground ">
          <BasicInformationSetting />
        </div>
      ) : (
        <div style={{ marginTop: '30px' }} className="bg-card text-card-foreground p-6 rounded-md shadow-sm">
          <PageContentEditor />
        </div>
      )}
    </div>
  );
}
