import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import { IconArrowLeft } from "@tabler/icons-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@radix-ui/react-scroll-area";
import { useNavigate, useParams } from "@tanstack/react-router";

export default function ChatProfileView() {
    const navigate = useNavigate()
    const { memberId } = useParams({ from: '/_authenticated/members/profile/$memberId/messages/$conversationId' });
    const backToChat = () => {
        navigate({
            to: "/members/profile/$memberId/messages",
            params: { memberId }
        });
    }
    return (
        <div className="grid grid-cols-1 2xl:grid-cols-[1fr_2fr_1fr] h-screen w-full gap-[20px]">
            {/* Left User Panel */}
            <div className="flex flex-col items-start gap-[10px]">
                <button onClick={backToChat} className="flex items-center justify-center cursor-pointer">
                    <IconArrowLeft size={20} />
                    Back
                </button>
                <div className="w-full border border-gray-200 p-4 space-y-4 rounded-lg">
                    <Avatar className="w-[35px] h-[35px] rounded-full bg-gray-700 text-white flex items-center justify-center">
                        <AvatarImage src="/avatar1.png" alt="Fit-n_Sexy_milf" />
                        <AvatarFallback>FS</AvatarFallback>
                    </Avatar>
                    <div className="text-sm font-medium">Fit-n_Sexy_milf (49)</div>
                    <div className="text-xs text-gray-500">Portarlington - VTL: 0</div>
                    <div className="text-xs text-gray-500">Timezone: GMT+5:30</div>
                    <div className="text-xs text-gray-700">Relation Status: Married</div>
                    <div className="text-xs text-gray-700">Location: United Kingdom</div>
                    <div className="text-xs text-gray-700">Interests: Sex toys, Life...</div>

                    <div>
                        <label className="text-xs font-semibold">Affiliate</label>
                        <div className="border rounded p-1 text-sm">No</div>
                    </div>

                    <div>
                        <label className="text-xs font-semibold">Notes</label>
                        <Textarea defaultValue="He is back after 3 days." className="mt-1 text-sm" />
                    </div>

                    <div className="text-sm space-y-1">
                        <div>
                            <strong>Name</strong>: John Doe
                        </div>
                        <div>
                            <strong>Living</strong>: Lives in Fulham, Splits bills with partner
                        </div>
                    </div>
                </div>
            </div>


            {/* Center Chat Panel */}
            <div className="border border-gray-200 flex flex-col rounded-lg">
                <div className="p-4 border-b flex items-center gap-2">

                    <Input placeholder="Search Keyword" className="w-full" />
                </div>

                <ScrollArea className="flex-1 p-4 space-y-4 overflow-auto">
                    {/* Example message block */}
                    <div className="text-xs text-gray-500">Sat 5:10 AM</div>
                    <div className="w-max max-w-[80%] p-2 rounded bg-gray-200 text-sm">next time you'll be awake at this hour why not now</div>
                    <div className="w-max max-w-[80%] p-2 rounded bg-gray-100 text-sm">Didn't I tell you not to put your phone on charge just because it's the weekend?</div>

                    <div className="text-xs text-gray-500 text-center mt-4">Tuesday Dec 2, 2024</div>
                    <div className="w-max max-w-[80%] p-2 rounded bg-gray-200 text-sm">next time you'll be awake at this hour why not now</div>
                    <div className="w-max max-w-[80%] p-2 rounded bg-gray-100 text-sm">Didn't I tell you not to put your phone on charge just because it's the weekend?</div>
                </ScrollArea>

                <div className="p-4 border flex gap-2 items-center rounded-b-lg">
                    <Input placeholder="Enter Message" className="flex-1" />
                    <Button variant="ghost">😊</Button>
                    <Button variant="ghost">❤️</Button>
                    <Button variant="default">Send</Button>
                </div>
            </div>

            {/* Right User Panel */}
            <div className="border border-gray-200 p-4 space-y-4 rounded-lg">
                <Avatar className="w-16 h-16">
                    <AvatarImage src="/avatar2.png" alt="Daniel Hamilton" />
                    <AvatarFallback>DH</AvatarFallback>
                </Avatar>
                <div className="text-sm font-medium">Daniel Hamilton</div>
                <div className="text-xs text-gray-500">Last Active: 11 Sep 2024, 1:45 PM</div>
                <div className="text-xs text-gray-500">Timezone: GMT+5:30</div>
                <div className="text-xs text-gray-700">Relation Status: Married</div>
                <div className="text-xs text-gray-700">Location: United Kingdom</div>
                <div className="text-xs text-gray-700">Interests: Sex toys, Life...</div>

                <div>
                    <label className="text-xs font-semibold">Affiliate</label>
                    <div className="border rounded p-1 text-sm">No</div>
                </div>

                <div>
                    <label className="text-xs font-semibold">Notes</label>
                    <Textarea defaultValue="He is back after 3 days." className="mt-1 text-sm" />
                </div>

                <div className="text-sm space-y-1">
                    <div>
                        <strong>Name</strong>: John Doe
                    </div>
                    <div>
                        <strong>Living</strong>: Lives in Fulham, Splits bills with partner
                    </div>
                </div>
            </div>
        </div>
    );
}
