import { useState } from 'react'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { SearchIcon } from 'lucide-react'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { DatePicker } from '@/components/date-picker'

interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
}

const domainOptions = [
  'uforpls.female.deso',
  'example.com',
  'testdomain.net',
  'sample.org',
  'demo.site',
]

const countryOptions = [
  'USA',
  'UK',
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Spain',
  'Italy',
  'Japan',
  'India',
]

const yesterdayOptions = [
  'Yesterday',
  'Today',
  'Last 7 days',
  'Last 30 days',
]

export function DataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const [filters, setFilters] = useState({
    domain: undefined as string | undefined,
    country: undefined as string | undefined,
    yesterday: undefined as string | undefined,
    fromDate: undefined as Date | undefined,
    toDate: undefined as Date | undefined,
  })

  const [hasSearched, setHasSearched] = useState(false)

  const handleFilterChange = (
    key: 'domain' | 'country' | 'yesterday' | 'fromDate' | 'toDate',
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleDateChange = (
    key: 'fromDate' | 'toDate',
    date: Date | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: date }))
  }

  const handleSearch = () => {
    // Apply filters to table
    if (filters.domain) {
      table.getColumn('domain')?.setFilterValue(filters.domain)
    }
    if (filters.country) {
      table.getColumn('country')?.setFilterValue(filters.country)
    }
    setHasSearched(true)
  }

  const handleReset = () => {
    setFilters({
      domain: undefined,
      country: undefined,
      yesterday: undefined,
      fromDate: undefined,
      toDate: undefined,
    })
    table.resetColumnFilters()
    setHasSearched(false)
  }


  const isFiltered = table.getState().columnFilters.length > 0
  // const isFiltered = Object.values(filters).some(value => value !== undefined)

  const hasActiveFilters = Boolean(
    (filters.domain && filters.domain.length > 0) ||
    (filters.country && filters.country.length > 0) ||
    (filters.yesterday && filters.yesterday.length > 0) ||
    (filters.fromDate) ||
    (filters.toDate)
  )

  return (
    <div className='flex flex-col gap-4'>
      {/* First row - 3 filter dropdowns */}
      <div className='flex flex-wrap gap-4'>
        <FilterSelect
          value={filters.domain}
          placeholder='Select Domain'
          options={domainOptions}
          onChange={(value) => handleFilterChange('domain', value)}
          className='w-[200px] bg-card'
        />

        <FilterSelect
          value={filters.country}
          placeholder='Select Country'
          options={countryOptions}
          onChange={(value) => handleFilterChange('country', value)}
          className='w-[200px] bg-card'
        />

        <FilterSelect
          value={filters.yesterday}
          placeholder='Yesterday'
          options={yesterdayOptions}
          onChange={(value) => handleFilterChange('yesterday', value)}
          className='w-[200px] bg-card'
        />

        <div className='flex items-center gap-2'>
          <DatePicker
            value={filters.fromDate}
            placeholder="From Date"
            onChange={(date) => {
              handleDateChange('fromDate', date)
              // If toDate is set and is earlier than the new fromDate, clear toDate
              if (filters.toDate && date && date > filters.toDate) {
                handleDateChange('toDate', undefined)
              }
            }}
            max={filters.toDate}
            className="w-[200px] bg-card h-9 rounded-md"
          />

          <DatePicker
            value={filters.toDate}
            placeholder="To Date"
            onChange={(date) => {
              // Only allow setting toDate if it's not earlier than fromDate
              if (!filters.fromDate || !date || date >= filters.fromDate) {
                handleDateChange('toDate', date)
              }
            }}
            min={filters.fromDate}
            className="w-[200px] bg-card h-9 rounded-md"
          />
        </div>

        <Button onClick={handleSearch} disabled={!hasActiveFilters} >
          {/* <SearchIcon className='mr-2 h-4 w-4' /> */}
          Search
        </Button>

        {(isFiltered || hasSearched) && (
          <Button variant='outline' onClick={handleReset}>
            Reset
          </Button>
        )}
      </div>
    </div >
  )
}
