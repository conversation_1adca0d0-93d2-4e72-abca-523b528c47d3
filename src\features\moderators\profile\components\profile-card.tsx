import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { END_POINTS } from '@/features/members/utils/constant'
import { formatDateToReadable } from '@/features/members/utils/utilities'
import { IconDotsVertical } from '@tabler/icons-react'
import { useNavigate } from '@tanstack/react-router'

interface ProfileCardProps {
  moderator: {
    id: string
    name: string
    moderatorType: string
    phoneNumber: string
    nickname: string
  }
}

export default function ProfileCard({ moderator }: any) {
  const navigate = useNavigate();
  const initials = moderator?.name
    .split(' ')
    .map((n: any) => n[0])
    .join('')
    .toUpperCase()

  const details = [
    { label: 'Role:', value: 'Moderator Use' },
    { label: 'Mobile:', value: moderator?.phone || "N/A" },
    { label: 'Email:', value: moderator?.email },
    { label: 'Joined:', value: formatDateToReadable(moderator?.createdAt) || "" },
    { label: 'Minimum Payout:', value: moderator?.user_profile?.minimumPayout || "N/A" },
    { label: 'Payment Currency:', value: moderator?.user_profile?.paymentCurrency || "N/A" },
    { label: 'Payment Method:', value: moderator?.user_profile?.paymentMethod || "N/A" },
    { label: 'Beneficiary Account Name:', value: moderator?.user_profile?.beneficiaryAccountName || "N/A" },
    { label: 'Beneficiary Account Address:', value: moderator?.user_profile?.beneficiaryAccountAddress || "N/A" },
    { label: 'Beneficiary Country:', value: moderator?.user_profile?.beneficiaryCountry || "N/A" },
    { label: 'Beneficiary City:', value: moderator?.user_profile?.beneficiaryCity || "N/A" },
    { label: 'Beneficiary Bank:', value: moderator?.user_profile?.beneficiaryBank || "N/A" },
    { label: 'Beneficiary Bank Address:', value: moderator?.user_profile?.beneficiaryBankAddress || "N/A" },
    { label: 'Account / Wallet Number:', value: moderator?.user_profile?.accountNumber || "N/A" },
    { label: 'Bank Code:', value: moderator?.user_profile?.bankCode || "N/A" },
    // { label: 'Personal ID / Photo:', value: moderator?.user_profile?.beneficiaryAccountName || "N/A" },
  ]

  const stats = [
    { label: 'Reply Percentage', value: '50%' },
    { label: 'Average Character / Message', value: '150' },
    { label: 'Total Hole Messages', value: '123' },
  ]

  return (
    <div className="space-y-6">
      <div className="bg-card rounded-2xl border overflow-hidden">
        <div className="p-4">
          <div className="flex items-start justify-between ">
            <div className="flex flex-col items-center w-full gap-2">
              <div className="relative">
                <Avatar className="h-25 w-25">
                  <AvatarImage src="" alt={moderator?.name} />
                  <AvatarFallback className="text-xl font-semibold bg-muted">
                    {initials}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute bottom-2 right-1 h-5 w-5 bg-muted-foreground border-2 border-background rounded-full"></div>
              </div>

              {/* Name */}
              <h2 className="text-lg font-bold text-center text-foreground">{moderator?.name}</h2>
            </div>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className=" top-4 right-4">
                  <IconDotsVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => navigate({ to: `${END_POINTS.UPDATE_MODERATOR}/${moderator?.id}` })}>
                  Edit Profile Details
                </DropdownMenuItem>
                <DropdownMenuItem>Reset Password</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">Block User</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Profile Details */}
        <div className="px-6 pb-6">
          <div className="space-y-3 flex flex-col gap-[10px]">
            {details.map((detail) => (
              <div key={detail.label} className="flex justify-between items-center mb-0">
                <div className="text-xs text-muted-foreground font-medium">{detail.label}</div>
                <div className="text-xs text-muted-foreground text-right max-w-[60%] truncate">{detail.value}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Stats Card */}
      <div className="border">
        <div className="flex gap-3 flex-wrap">
          {stats.map((stat) => (
            <div key={stat.label} className="bg-card border rounded-2xl p-4 flex flex-col gap-2 flex-1">
                <span className="text-2xl font-bold text-muted-foreground bg-accent rounded-full flex items-center justify-center min-w-[56px] w-[56px] h-[56px]">$</span>
                <span className="text-sm font-medium text-muted-foreground ">{stat.label}</span> 
                <p className="text-xl font-bold text-foreground">{stat.value}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
