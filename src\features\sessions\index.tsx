import { Main } from "@/components/layout/main";
import { LobyPrimaryButtons } from "./components/models-primary-buttons";
import ModelsProvider from "./context/models-context";
import { LobyTable } from "./components/models-table";
import { columns } from "./components/models-columns";

export default function List() {

  return (
    <ModelsProvider>
      <Main>
        <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Loby
            </h2>
          </div>
          <LobyPrimaryButtons />
        </div>
        <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
          <LobyTable columns={columns} />
        </div>
      </Main>
    </ModelsProvider>
  );
}
